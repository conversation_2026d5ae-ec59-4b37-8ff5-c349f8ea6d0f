version: '3.8'

services:
  finagentx:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: finagentx-app
    restart: unless-stopped
    
    # 环境变量
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - LANG=C.UTF-8
      - LC_ALL=C.UTF-8
      # 可以通过环境变量覆盖配置
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY:-}
      - LLM_MODEL=${LLM_MODEL:-deepseek/deepseek-chat-v3-0324:free}
      - DEBUG=${DEBUG:-false}
    
    # 端口映射（如果需要Web服务）
    ports:
      - "8000:8000"
    
    # 数据卷挂载
    volumes:
      # 挂载数据目录
      - ./data:/app/data
      # 挂载输出目录
      - ./outputs:/app/outputs
      - ./analysis_reports:/app/analysis_reports
      - ./report_images:/app/report_images
      # 挂载日志目录
      - ./logs:/app/logs
      # 挂载配置文件（如果需要自定义）
      - ./config:/app/config
    
    # 工作目录
    working_dir: /app
    
    # 健康检查
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    
    # 网络
    networks:
      - finagentx-network

  # Redis缓存服务（可选）
  redis:
    image: redis:7-alpine
    container_name: finagentx-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - finagentx-network
    command: redis-server --appendonly yes

# 数据卷
volumes:
  redis_data:
    driver: local

# 网络
networks:
  finagentx-network:
    driver: bridge
