# FinAgentX - 智能金融研报生成系统

## 🎯 项目简介

FinAgent 是一个基于大语言模型的智能金融研报生成系统，能够自动收集、分析和生成专业的公司研究报告。系统集成了数据爬取、财务分析、市场观点收集、图表生成等多个功能模块，为投资者和分析师提供全面、准确的投资决策支持。

## ✨ 核心功能

### 📊 数据收集与分析
- **财务数据爬取**: 自动获取公司基本信息、财务数据、股价信息
- **市场观点收集**: 从股吧等平台收集投资者观点和市场情绪
- **新闻资讯整合**: 收集相关新闻和行业动态

### 📈 智能分析
- **财务分析**: 基于LLM的深度财务数据分析
- **技术分析**: 股价走势和技术指标分析
- **市场情绪分析**: 投资者观点和市场情绪量化分析

### 📋 报告生成
- **专业研报**: 生成结构化的投资研究报告
- **可视化图表**: 自动生成财务图表和技术分析图
- **多格式输出**: 支持Markdown、PDF等多种格式

## 🚀 快速开始

### 环境要求
- Python 3.8+
- 所需依赖包（见requirements.txt）

### 安装步骤

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **配置设置**
```bash
# 编辑配置文件，填入API密钥等信息
# 配置文件位于 config.py
```

### 基本使用（双接口格式）

#### 🚀 简化格式（推荐）
```bash
# 使用公司名称（自动映射股票代码）
python run_company_research_report.py 商汤科技
python run_company_research_report.py 4Paradigm
python run_company_research_report.py 腾讯控股

# 使用股票代码（自动识别公司名称）
python run_company_research_report.py 00020.HK
python run_company_research_report.py 06682.HK
python run_company_research_report.py 00700.HK

# 跳过某些步骤（快速测试）
python run_company_research_report.py 商汤科技 --skip_steps 2 5
```

#### 📋 完整格式（明确指定）
```bash
# 明确指定公司名称和股票代码
python run_company_research_report.py --company_name 商汤科技 --company_code 00020.HK
python run_company_research_report.py --company_name 4Paradigm --company_code 06682.HK
python run_company_research_report.py --company_name 腾讯控股 --company_code 00700.HK

# 带参数的完整格式
python run_company_research_report.py --company_name 商汤科技 --company_code 00020.HK --skip_steps 2 --timeout 1800
```

### 支持的输入格式
- ✅ **公司名称**: 4Paradigm, 平安银行, 腾讯控股
- ✅ **港股代码**: 06682.HK, 00700.HK, 09988.HK
- ✅ **A股代码**: 000001.SZ, 600036.SH, 600519.SH
- ✅ **美股代码**: AAPL, MSFT, TSLA

## 📁 核心文件结构

```
FinAgentX/
├── run_company_research_report.py  # 🚀 主程序入口（单参数智能接口）
├── crawler.py                      # 📊 数据爬取模块
├── crawler_viewpoint.py           # 💬 观点爬取模块
├── visualize_report.py            # 📈 图表生成模块
├── financial_agent.py             # 💰 财务分析模块
├── guba_viewpoint_agent.py        # 🗣️ 股吧观点分析模块
├── research_report_generator.py   # 📋 研报生成模块
├── config.py                      # ⚙️ 配置文件
└── README.md                      # 📖 项目说明
```

## 🔧 核心模块详细说明

### 1. 📊 数据爬取模块 (crawler.py)
**功能概述**: 从东方财富等数据源自动爬取公司财务数据和基本信息

**核心特性**:
- **动态公司识别**: 从环境变量读取公司代码，支持港股、A股、美股
- **多维度数据采集**:
  - 主要财务指标 (营收、利润、资产等60+指标)
  - 利润表数据 (收入结构、成本分析等)
  - 资产负债表 (资产结构、负债情况等)
  - 现金流量表 (经营、投资、融资现金流)
  - 行业对比数据 (成长性、估值、规模对比)
- **智能数据映射**: 自动将英文字段映射为中文表头，便于分析
- **数据质量保证**: 自动过滤无效数据，保留核心财务指标
- **容错机制**: 网络异常时自动重试，确保数据完整性

**技术实现**:
```python
# 支持的数据源API
- 东方财富主要指标API
- 财务报表详细数据API
- 行业对比分析API
- 实时股价数据API
```

**输出文件**:
- `主要指标.csv` - 核心财务指标汇总
- `利润表.csv` - 详细利润表数据
- `资产负债表.csv` - 资产负债结构
- `现金流量表.csv` - 现金流分析数据
- `成长性对比.csv` - 行业成长性对比
- `估值对比.csv` - 估值水平对比
- `规模对比.csv` - 公司规模对比

### 2. 💬 观点爬取模块 (crawler_viewpoint.py)
**功能概述**: 从东方财富股吧爬取投资者观点和市场讨论

**核心特性**:
- **动态URL生成**: 根据股票代码自动生成正确的股吧URL
  - 港股: `00020.HK` → `hk00020` → `https://guba.eastmoney.com/list,hk00020,99,j_1.html`
  - A股深交所: `000001.SZ` → `sz000001`
  - A股上交所: `600036.SH` → `sh600036`
- **多页面爬取**: 支持批量爬取多个页面的讨论内容
- **完整内容获取**: 不仅获取标题，还深入爬取帖子正文内容
- **反爬虫机制**: 内置随机延迟和请求头伪装，避免被封IP
- **数据结构化**: 提取标题、内容、阅读数、回复数等结构化信息

**技术实现**:
```python
# 股吧代码转换逻辑
def get_guba_stock_code(stock_code):
    if stock_code.endswith('.HK'):
        return f'hk{stock_code.replace(".HK", "")}'
    elif stock_code.endswith('.SZ'):
        return f'sz{stock_code.replace(".SZ", "")}'
    elif stock_code.endswith('.SH'):
        return f'sh{stock_code.replace(".SH", "")}'
```

**输出文件**:
- `guba_viewpoint.csv` - 包含标题、内容、URL、阅读数、回复数等字段

### 3. 📈 图表生成模块 (visualize_report.py)
**功能概述**: 基于财务数据生成专业的可视化图表和分析报告

**核心特性**:
- **智能字体管理**: 自动检测和加载中文字体，完美支持中文显示
- **8类专业图表**:
  1. **财务概览仪表板** - 关键指标一览
  2. **估值对比图** - 市盈率、市净率行业对比
  3. **成长性对比图** - 营收、EPS增长率对比
  4. **规模对比图** - 营业收入规模对比
  5. **营收利润趋势图** - 多年度趋势分析
  6. **盈利能力趋势图** - 毛利率、净利率变化
  7. **利润表结构分析** - 收入成本结构饼图
  8. **现金流分析图** - 三大现金流对比
  9. **估值分析图** - 估值指标历史变化
  10. **综合评分雷达图** - 多维度综合评估
- **LLM图表描述**: 为每个图表生成专业的文字描述和分析
- **高质量输出**: 支持PNG格式，300DPI高清输出
- **自适应布局**: 根据数据自动调整图表布局和样式

**技术实现**:
```python
# 支持的图表库
- matplotlib: 基础图表绘制
- seaborn: 统计图表美化
- plotly: 交互式图表(可选)

# 字体支持
- Windows: SimHei, Microsoft YaHei
- macOS: PingFang SC, STHeiti
- Linux: WenQuanYi, Noto Sans CJK
```

### 4. 💰 财务分析模块 (financial_agent.py)
**功能概述**: 基于大语言模型的智能财务数据深度分析

**核心特性**:
- **LLM驱动分析**: 使用DeepSeek等先进模型进行财务数据解读
- **全量数据分析**: 直接分析完整的CSV财务数据，不遗漏任何信息
- **专业报告生成**: 输出结构化的Markdown格式财务分析报告
- **多维度评估**:
  - 财务表现分析 (营收、利润、增长趋势)
  - 盈利能力评估 (毛利率、净利率、ROE等)
  - 成长性分析 (同比增长率、环比变化)
  - 财务健康度 (资产负债率、现金流状况)
  - 估值水平分析 (PE、PB等估值指标)
- **智能洞察**: AI自动识别财务数据中的关键问题和亮点
- **投资建议**: 基于财务分析给出专业的投资建议和风险提示

**技术实现**:
```python
# LLM配置
model: "deepseek/deepseek-chat-v3-0324:free"
temperature: 0.1  # 确保分析的客观性
max_tokens: 4000  # 支持长篇分析报告
```

**输出文件**:
- `财务分析报告_[时间戳].md` - 完整的财务分析报告

### 5. 🗣️ 股吧观点分析模块 (guba_viewpoint_agent.py)
**功能概述**: 对股吧观点数据进行清洗、分析和情绪量化

**核心特性**:
- **数据清洗**: 自动过滤无效、重复和垃圾内容
- **情绪分析**: 使用NLP技术分析投资者情绪倾向
- **观点分类**: 将观点分为看多、看空、中性等类别
- **热点提取**: 识别讨论热点和关注焦点
- **情绪量化**: 将定性的市场情绪转化为定量指标
- **批量处理**: 支持大量观点数据的批量分析
- **来源标注**: 保留观点来源，确保分析的可追溯性

**技术实现**:
```python
# 分析维度
- 情绪极性: 正面/负面/中性
- 置信度: 观点的可信程度
- 影响力: 基于阅读数和回复数
- 时效性: 观点的时间相关性
```

**输出文件**:
- `guba_viewpoint_analysis.json` - 结构化的观点分析结果

### 6. 📋 研报生成模块 (research_report_generator.py)
**功能概述**: 整合所有分析结果，生成完整的投资研究报告

**核心特性**:
- **多Agent协同**: 集成财务分析、观点分析、图表等多个模块的结果
- **智能报告结构**: 自动生成专业的研报结构和章节
- **多模态整合**: 结合文字分析、数据图表、市场观点等多种信息
- **投资建议生成**: 基于综合分析给出明确的投资建议和评级
- **风险评估**: 识别和量化投资风险因素
- **多格式输出**: 支持Markdown、Word、PDF等多种格式
- **专业术语**: 使用标准的金融分析术语和表达方式

**报告结构**:
```markdown
1. 执行摘要 - 核心观点和投资建议
2. 公司概况 - 基本信息和业务描述
3. 财务分析 - 详细的财务数据分析
4. 估值分析 - 估值水平和合理价位
5. 技术分析 - 股价走势和技术指标
6. 市场观点 - 投资者情绪和市场预期
7. 风险因素 - 主要风险点识别
8. 投资建议 - 明确的投资评级和建议
```

**技术实现**:
```python
# 支持的输出格式
- Markdown: 便于在线查看和编辑
- Word: 专业文档格式
- PDF: 正式报告格式
- HTML: 网页展示格式
```

### 7. 🚀 主程序 (run_company_research_report.py)
**功能概述**: 系统的核心调度器，管理整个研报生成流水线

**核心特性**:
- **双接口支持**:
  - 简化格式: `python run.py 商汤科技`
  - 完整格式: `python run.py --company_name 商汤科技 --company_code 00020.HK`
- **智能公司识别**: 自动区分公司名称和股票代码，支持中英文
- **流水线管理**: 按序执行6个步骤，支持跳过特定步骤
- **环境变量传递**: 确保所有子模块使用正确的公司信息
- **错误处理**: 单步失败不影响整体流程，提供详细错误信息
- **进度跟踪**: 实时显示执行进度和耗时统计
- **执行报告**: 自动生成详细的执行报告和日志

**支持的公司格式**:
```python
# 公司名称映射
COMPANY_MAPPING = {
    '00020.HK': {'name': '商汤科技', 'aliases': ['商汤科技', '商汤-W', '商汤', 'SenseTime']},
    '06682.HK': {'name': '4Paradigm', 'aliases': ['4Paradigm', '第四范式']},
    '00700.HK': {'name': '腾讯控股', 'aliases': ['腾讯控股', '腾讯', 'Tencent']},
    # ... 更多公司映射
}
```

### 8. ⚙️ 配置管理 (config.py)
**功能概述**: 统一管理系统的所有配置参数

**配置模块**:
- **LLM配置**: API密钥、模型选择、请求参数
- **数据源配置**: 爬取目标、超时设置、重试次数
- **输出配置**: 文件格式、保存路径、图表样式
- **分析配置**: 分析周期、指标阈值、评级标准

## 🏗️ 系统架构与模块交互

### 技术架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    FinAgentX 智能研报生成系统                    │
├─────────────────────────────────────────────────────────────┤
│  🚀 主程序调度器 (run_company_research_report.py)              │
│  ├── 双接口支持 (简化格式 + 完整格式)                           │
│  ├── 智能公司识别 (名称/代码自动映射)                           │
│  ├── 流水线管理 (6步骤顺序执行)                                │
│  └── 环境变量传递 (统一公司信息)                               │
├─────────────────────────────────────────────────────────────┤
│                        核心处理模块                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
│  │📊 数据爬取   │  │💬 观点爬取   │  │📈 图表生成   │           │
│  │crawler.py   │  │crawler_     │  │visualize_   │           │
│  │             │  │viewpoint.py │  │report.py    │           │
│  │• 财务数据    │  │• 股吧观点    │  │• 8类专业图表 │           │
│  │• 7个CSV文件  │  │• 动态URL    │  │• LLM图表描述 │           │
│  │• 行业对比    │  │• 反爬机制    │  │• 中文字体    │           │
│  └─────────────┘  └─────────────┘  └─────────────┘           │
│           │               │               │                  │
│           ▼               ▼               ▼                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
│  │💰 财务分析   │  │🗣️ 观点分析   │  │📋 研报生成   │           │
│  │financial_   │  │guba_viewpoint│  │research_    │           │
│  │agent.py     │  │_agent.py    │  │report_      │           │
│  │             │  │             │  │generator.py │           │
│  │• LLM深度分析 │  │• 情绪分析    │  │• 多Agent协同 │           │
│  │• 专业报告    │  │• 观点分类    │  │• 完整研报    │           │
│  │• 投资建议    │  │• 情绪量化    │  │• 多格式输出  │           │
│  └─────────────┘  └─────────────┘  └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                        支撑服务                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐           │
│  │⚙️ 配置管理   │  │🔧 超时管理   │  │📝 日志系统   │           │
│  │config.py    │  │timeout_     │  │logging &    │           │
│  │             │  │config.py    │  │error        │           │
│  │• LLM配置     │  │• 动态超时    │  │handling     │           │
│  │• 统一参数    │  │• 步骤优化    │  │• 详细追踪    │           │
│  └─────────────┘  └─────────────┘  └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

### 数据流向图
```
用户输入 → 主程序 → 环境变量设置
    ↓
步骤1: 数据爬取 → 7个CSV文件 (财务数据)
    ↓
步骤2: 观点爬取 → guba_viewpoint.csv (市场观点)
    ↓
步骤3: 图表生成 → 11个PNG图表 + 图表描述
    ↓
步骤4: 财务分析 → 财务分析报告.md (LLM分析)
    ↓
步骤5: 观点分析 → 观点分析结果.json (情绪量化)
    ↓
步骤6: 研报生成 → 完整投资研究报告 (多格式)
    ↓
输出文件: 执行报告 + 分析报告 + 图表文件
```

### 模块间通信机制
1. **环境变量传递**: 主程序通过环境变量向所有子模块传递公司信息
2. **文件接口**: 模块间通过标准化的CSV/JSON文件交换数据
3. **配置共享**: 所有模块共享config.py中的统一配置
4. **错误隔离**: 单个模块失败不影响其他模块的执行

## ⚙️ 配置说明

### config.py 配置文件
```python
# LLM配置
class LLMConfig:
    model = "deepseek/deepseek-r1-0528:free"
    base_url = "https://openrouter.ai/api/v1"
    api_key = "your_api_key_here"
    temperature = 0.1
    max_tokens = 4000

# 数据源配置
class DataConfig:
    enable_crawler = True
    enable_viewpoint = True
    timeout = 30
    retry_times = 3

# 输出配置
class OutputConfig:
    report_format = "markdown"
    chart_format = "png"
    output_dir = "outputs"
```

### 必需配置项
1. **API密钥**: 在config.py中设置OpenRouter API密钥
2. **模型选择**: 推荐使用deepseek/deepseek-r1-0528:free（免费且效果好）
3. **输出目录**: 确保有写入权限

## 📊 6步研报生成流程

系统按以下6个步骤自动生成完整研报：

### 步骤1: 数据爬取 (crawler.py)
- 🔍 **公司基础信息**: 公司名称、行业分类、主营业务
- 📈 **财务数据**: 营收、利润、资产负债等核心财务指标
- 📰 **新闻资讯**: 最新公司新闻、公告、行业动态
- ⏱️ **预计耗时**: 30-60秒

### 步骤2: 观点爬取 (crawler_viewpoint.py)
- 💬 **投资者观点**: 从股吧等平台收集投资者讨论
- 😊 **市场情绪**: 提取正面、负面、中性情绪
- 🔥 **热点话题**: 识别当前讨论热点和关注焦点
- ⏱️ **预计耗时**: 45-90秒

### 步骤3: 图表生成 (visualize_report.py)
- 📊 **财务图表**: 营收利润趋势、财务指标对比
- 📈 **股价图表**: 股价走势、技术指标分析
- 🎯 **专业图表**: 8类专业财务分析图表
- ⏱️ **预计耗时**: 20-40秒

### 步骤4: 财务分析 (financial_agent.py)
- 🧮 **深度分析**: 基于LLM的财务数据深度解读
- 📋 **指标计算**: 盈利能力、成长性、财务健康度
- 💡 **专业见解**: AI生成的专业财务分析观点
- ⏱️ **预计耗时**: 60-120秒

### 步骤5: 观点分析 (guba_viewpoint_agent.py)
- 🎭 **情绪分析**: 量化投资者情绪和市场预期
- 🔍 **观点提取**: 提取关键投资观点和争议点
- 📊 **情绪指标**: 生成市场情绪量化指标
- ⏱️ **预计耗时**: 45-90秒

### 步骤6: 研报生成 (research_report_generator.py)
- 📝 **报告整合**: 整合所有分析结果
- 📋 **结构化输出**: 生成专业的投资研究报告
- 🎯 **投资建议**: 基于综合分析的投资建议和风险提示
- ⏱️ **预计耗时**: 90-180秒

### 总耗时: 约5-10分钟

## 📈 生成的研报内容

### 完整研报包含：
1. **📋 执行摘要**: 关键发现和投资建议概览
2. **🏢 公司概况**: 基本信息、业务描述、行业地位
3. **💰 财务分析**: 关键财务指标、盈利能力、财务健康度
4. **📊 技术分析**: 股价走势、技术指标、支撑阻力位
5. **💬 市场观点**: 投资者情绪、热点话题、争议分析
6. **🎯 投资建议**: 综合评级、目标价位、风险提示

### 专业图表包含：
- 📊 **财务数据趋势图**: 营收、利润、资产变化趋势
- 📈 **股价走势图**: 技术分析和关键价位标注
- 🎯 **财务指标对比图**: 与行业平均水平对比
- 😊 **市场情绪分析图**: 投资者情绪量化展示

## 🛠️ 高级功能

### 跳过特定步骤
```bash
# 跳过观点爬取和图表生成
python run_company_research_report.py 4Paradigm --skip_steps 2 3

# 只运行财务分析
python run_company_research_report.py 平安银行 --skip_steps 1 2 3 5 6
```

### 自定义超时时间
```bash
# 设置单个步骤超时为10分钟
python run_company_research_report.py 腾讯控股 --timeout 600
```

### 批量处理示例
```python
# 批量生成多个公司的研报
companies = ["4Paradigm", "06682.HK", "平安银行", "000001.SZ"]

for company in companies:
    os.system(f"python run_company_research_report.py {company}")
```

## 🔍 故障排除与最佳实践

### 常见问题解决

1. **❌ 缺少必要脚本文件错误**
   ```
   ERROR - ❌ 缺少必要的脚本文件: crawler.py, crawler_viewpoint.py...
   ```
   **解决方案**: 确保在正确的目录下运行
   ```bash
   cd docker_image/docker_image/app
   python run_company_research_report.py 商汤科技
   ```

2. **🌐 观点数据爬取超时**
   ```
   INFO - ❌ 失败 观点数据爬取 - 耗时: 1800.0秒 执行超时
   ```
   **解决方案**:
   - 跳过观点相关步骤: `--skip_steps 2 5`
   - 增加超时时间: `--timeout 2400`
   - 检查网络连接稳定性

3. **🔑 API密钥错误**
   ```
   ERROR - API调用失败: 401 Unauthorized
   ```
   **解决方案**:
   - 检查config.py中的API密钥配置
   - 确认OpenRouter账户有足够额度
   - 验证API密钥格式正确

4. **📊 图表生成中文显示问题**
   ```
   WARNING - 字体加载失败，中文可能显示为方块
   ```
   **解决方案**:
   - Windows: 确保系统有SimHei或Microsoft YaHei字体
   - Linux: 安装中文字体包
   - macOS: 系统自带中文字体支持

5. **🏢 公司名称无法识别**
   ```
   WARNING - 未知股票代码格式，使用默认值
   ```
   **解决方案**:
   - 使用完整格式明确指定: `--company_name 公司名 --company_code 股票代码`
   - 检查公司名称是否在支持列表中
   - 提交新公司信息到映射表

### 🎯 最佳实践指南

#### 1. 推荐使用模式
```bash
# 🥇 最佳: 快速测试模式（跳过耗时步骤）
python run_company_research_report.py 商汤科技 --skip_steps 2 5

# 🥈 标准: 跳过观点爬取（平衡效率和完整性）
python run_company_research_report.py 商汤科技 --skip_steps 2

# 🥉 完整: 生成最完整报告（需要更长时间）
python run_company_research_report.py 商汤科技
```

#### 2. 网络环境优化
- **稳定网络**: 确保网络连接稳定，避免频繁断线
- **代理设置**: 如需要，配置合适的网络代理
- **防火墙**: 确保防火墙不阻止相关网络请求

#### 3. 性能优化建议
- **内存管理**: 建议4GB以上内存，避免大数据处理时内存不足
- **磁盘空间**: 预留至少1GB空间存储生成的报告和图表
- **并发控制**: 避免同时运行多个实例，可能导致API限流

#### 4. 数据质量保证
- **及时更新**: 定期更新公司映射表，支持更多公司
- **数据验证**: 生成报告后检查关键数据的合理性
- **多次验证**: 重要决策前建议多次生成报告对比

### 🛡️ 系统稳定性特性
- ✅ **编码兼容**: 完美支持中文Windows/Linux/macOS环境
- ✅ **错误恢复**: 单步失败不影响整体流程，自动跳过问题步骤
- ✅ **详细日志**: 提供详细的执行状态和错误信息，便于问题定位
- ✅ **智能识别**: 自动识别公司名称和股票代码，支持多种输入格式
- ✅ **超时管理**: 智能超时设置，避免长时间等待
- ✅ **重试机制**: 网络请求失败时自动重试，提高成功率

## 💡 使用技巧

### 最佳实践
1. **首次使用**: 建议先用知名公司测试，如`python run_company_research_report.py 腾讯控股`
2. **网络环境**: 确保网络稳定，避免在网络不稳定时运行
3. **API额度**: 注意OpenRouter API使用额度，避免超限
4. **存储空间**: 确保有足够磁盘空间存储生成的报告和图表

### 输出文件说明
- **执行报告**: `execution_report_[股票代码]_[时间戳].md`
- **研报文件**: 各模块会生成对应的分析报告
- **图表文件**: 保存在相应的图表目录中
- **日志文件**: 详细的执行日志便于问题排查

## 🎯 项目优势

### 🚀 **技术优势**
- **单参数接口**: 极简使用，一个参数搞定
- **智能识别**: 自动区分公司名称和股票代码
- **编码兼容**: 彻底解决中文编码问题
- **模块化设计**: 8个核心模块，功能清晰

### 📊 **功能优势**
- **全流程自动化**: 从数据收集到报告生成全自动
- **多维度分析**: 财务、技术、情绪多角度分析
- **专业图表**: 8类专业财务分析图表
- **LLM驱动**: 基于大语言模型的深度分析

### 🛡️ **稳定性优势**
- **错误容错**: 单步失败不影响整体流程
- **编码处理**: 支持各种编码格式，不会因编码问题中断
- **重试机制**: 网络问题自动重试
- **详细日志**: 完善的日志系统便于问题定位

### 💼 **商业价值**
- **投资决策支持**: 为投资者提供全面的决策依据
- **效率提升**: 自动化生成专业研报，大幅提升效率
- **成本节约**: 减少人工分析成本
- **标准化输出**: 确保分析报告的专业性和一致性

## 📞 联系与支持

### 使用帮助
- 📖 **详细文档**: 参考本README和代码注释
- 🐛 **问题反馈**: 遇到问题请提供详细的错误日志
- 💡 **功能建议**: 欢迎提出改进建议

### 系统要求
- **操作系统**: Windows 10+, Linux, macOS
- **Python版本**: 3.8+
- **内存要求**: 建议4GB以上
- **网络要求**: 稳定的互联网连接

---

## 🎉 开始使用

现在就开始使用FinAgentX生成您的第一份智能研报：

```bash
# 最简单的使用方式
python run_company_research_report.py 4Paradigm
```

**FinAgentX** - 让投资分析更智能，让决策更精准！

---

*最后更新: 2025年7月29日*
*版本: v2.1 - 双接口格式 + 动态公司识别版*
4. **盈利能力趋势** - 盈利质量变化
5. **利润表结构分析** - 收入成本结构
6. **现金流分析** - 现金流状况
7. **估值分析** - 估值水平变化
8. **综合评分雷达图** - 多维度综合评估

## 🔍 示例输出

以商汤-W (00020.HK)为例：

```markdown
# 商汤-W (00020.HK) 财务分析报告

**报告生成时间**: 2025-07-14 18:26:42  
**分析期间**: 2022-12-31 至 2024-12-31  
**数据来源**: 主要财务指标

## 📊 执行摘要
- 营业收入：37.721亿港元（2024年恢复增长+10.75%）
- 归母净利润：-42.784亿港元（亏损收窄33.57%）
- 净利润率：-114.17%（仍深度亏损但有改善）
- 投资建议：谨慎观望，关注盈利拐点

## 💡 投资建议
- **投资策略**: 中性偏谨慎
- **关键风险**: 持续亏损、现金流压力
- **关注指标**: 收入增长率、亏损收窄程度
```

## ⚠️ 注意事项

1. **数据质量**: 确保CSV数据完整准确
2. **网络连接**: 需要稳定的网络连接访问OpenRouter API
3. **API限制**: 免费模型可能有调用频率限制
4. **分析局限**: 分析基于历史数据，不构成投资建议

## 🛠️ 自定义配置

可以通过修改`config.py`来调整：
- LLM模型和参数
- 分析期数和输出格式
- API密钥和超时设置

## 📞 技术支持

如遇问题，请检查：
1. 数据文件格式是否正确
2. 网络连接是否正常
3. API密钥是否有效
4. 依赖包是否完整安装

---

*本系统由FinAgentX开发，基于DeepSeek R1模型提供智能财务分析服务*
