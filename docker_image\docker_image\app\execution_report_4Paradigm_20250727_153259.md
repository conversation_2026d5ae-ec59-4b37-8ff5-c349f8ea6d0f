
# 公司研报生成执行报告

## 基本信息
- **公司名称**: 4Paradigm
- **股票代码**: 4Paradigm
- **开始时间**: 2025-07-27 15:19:18
- **结束时间**: 2025-07-27 15:32:59
- **总耗时**: 821.0秒 (13.7分钟)

## 执行步骤详情

### 1. 数据爬取 (crawler.py)
- **状态**: ✅ 成功
- **耗时**: 12.8秒
- **时间**: 15:19:31
- **详情**: 无

### 2. 可视化图表生成 (visualize_report.py)
- **状态**: ✅ 成功
- **耗时**: 122.1秒
- **时间**: 15:21:33
- **详情**: 无

### 3. 财务分析 (financial_agent.py)
- **状态**: ✅ 成功
- **耗时**: 83.3秒
- **时间**: 15:22:56
- **详情**: 无

### 4. 股吧观点分析 (guba_viewpoint_agent.py)
- **状态**: ❌ 失败
- **耗时**: 600.0秒
- **时间**: 15:32:56
- **详情**: 执行超时

### 5. 研究报告生成 (research_report_generator.py)
- **状态**: ❌ 失败
- **耗时**: 2.7秒
- **时间**: 15:32:59
- **详情**: 错误: Traceback (most recent call last):

  File "C:\Users\<USER>\Desktop\results\docker_image\docker_image...
