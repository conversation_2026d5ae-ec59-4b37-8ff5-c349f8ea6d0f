#!/usr/bin/env python3
"""
独立的行业研究报告生成器
支持通过命令行参数指定行业名称生成专业的行业研究报告

使用方法:
python run_industry_research_report.py --industry_name "人工智能"
python run_industry_research_report.py --industry_name "新能源汽车" --format html
"""

import argparse
import asyncio
import os
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH

# 导入现有模块
from data_collector import DataCollector
from llm_client import FinancialAnalysisAgent
from chart_generator import ChartGenerator
from config import get_config, REPORT_TYPES

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IndustryResearchGenerator:
    """独立的行业研究报告生成器"""
    
    def __init__(self):
        """初始化行业研究生成器"""
        self.data_collector = DataCollector()
        self.analysis_agent = FinancialAnalysisAgent()
        self.chart_generator = ChartGenerator()
        self.config = get_config()
        
        # 行业名称映射字典
        self.industry_mapping = {
            "人工智能": "AI",
            "新能源汽车": "新能源汽车",
            "医疗健康": "医疗",
            "金融科技": "金融科技",
            "电子商务": "电商",
            "生物医药": "生物医药",
            "新材料": "新材料",
            "5G通信": "通信",
            "云计算": "云计算",
            "大数据": "大数据",
            "物联网": "物联网",
            "区块链": "区块链",
            "智能制造": "制造业",
            "清洁能源": "清洁能源",
            "半导体": "半导体",
            "航空航天": "航空航天"
        }
        
        logger.info("行业研究生成器初始化完成")
    
    async def collect_industry_data(self, industry_name: str) -> Dict[str, Any]:
        """
        收集行业相关数据
        
        Args:
            industry_name: 行业名称
            
        Returns:
            包含行业数据的字典
        """
        logger.info(f"开始收集行业数据: {industry_name}")
        
        try:
            # 获取行业分类数据
            industry_data = self.data_collector.get_industry_data()
            
            # 获取宏观经济数据作为背景
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = datetime.now().replace(year=datetime.now().year-2).strftime('%Y%m%d')
            
            macro_indicators = ["GDP", "CPI", "PPI"]
            macro_data = {}
            for indicator in macro_indicators:
                try:
                    data = self.data_collector.get_macro_data(indicator, start_date, end_date)
                    if not data.empty:
                        macro_data[indicator] = data
                except Exception as e:
                    logger.warning(f"获取{indicator}数据失败: {e}")
            
            # 获取市场情绪数据
            market_sentiment = self.data_collector.get_market_sentiment()
            
            data_package = {
                "industry_name": industry_name,
                "industry_classification": industry_data,
                "macro_data": macro_data,
                "market_sentiment": market_sentiment,
                "collection_timestamp": datetime.now().isoformat(),
                "data_sources": ["Tushare", "公开市场数据"]
            }
            
            logger.info(f"行业数据收集完成: {industry_name}")
            return data_package
            
        except Exception as e:
            logger.error(f"收集行业数据时出错: {e}")
            return {"error": str(e), "industry_name": industry_name}
    
    async def analyze_industry(self, industry_name: str, data_package: Dict[str, Any]) -> Dict[str, str]:
        """
        进行行业分析
        
        Args:
            industry_name: 行业名称
            data_package: 行业数据包
            
        Returns:
            包含各个分析章节的字典
        """
        logger.info(f"开始行业分析: {industry_name}")
        
        analysis_sections = {}
        
        try:
            # 准备数据上下文
            data_context = self._prepare_industry_context(industry_name, data_package)
            
            # 生成各个分析章节
            sections = [
                ("executive_summary", "执行摘要"),
                ("industry_overview", "行业概况"),
                ("market_analysis", "市场分析"),
                ("competitive_landscape", "竞争格局"),
                ("policy_analysis", "政策分析"),
                ("investment_opportunities", "投资机会"),
                ("risk_assessment", "风险评估"),
                ("conclusion", "结论与建议")
            ]
            
            for section_key, section_name in sections:
                logger.info(f"生成章节: {section_name}")
                
                content = await self._generate_section_content(
                    section_key, industry_name, data_context
                )
                analysis_sections[section_key] = content
                
                # 避免API限流
                await asyncio.sleep(1)
            
            logger.info(f"行业分析完成: {industry_name}")
            return analysis_sections
            
        except Exception as e:
            logger.error(f"行业分析时出错: {e}")
            return {"error": str(e)}
    
    def _prepare_industry_context(self, industry_name: str, data_package: Dict[str, Any]) -> str:
        """准备行业分析的数据上下文"""
        context_parts = [
            f"行业名称: {industry_name}",
            f"数据收集时间: {data_package.get('collection_timestamp', '未知')}",
        ]
        
        # 添加宏观数据摘要
        macro_data = data_package.get('macro_data', {})
        if macro_data:
            context_parts.append("宏观经济背景:")
            for indicator, data in macro_data.items():
                if not data.empty:
                    latest_value = data.iloc[-1] if len(data) > 0 else "无数据"
                    context_parts.append(f"  {indicator}: {latest_value}")
        
        # 添加市场情绪数据
        sentiment = data_package.get('market_sentiment', {})
        if sentiment:
            context_parts.append("市场情绪指标:")
            for key, value in sentiment.items():
                context_parts.append(f"  {key}: {value}")
        
        return "\n".join(context_parts)
    
    async def _generate_section_content(self, section_type: str, industry_name: str, data_context: str) -> str:
        """生成特定章节的内容"""
        
        section_prompts = {
            "executive_summary": f"""
            请为{industry_name}行业撰写执行摘要，包括：
            1. 行业发展现状和规模
            2. 主要增长驱动因素
            3. 竞争格局概述
            4. 投资机会和建议
            要求专业全面，约300-400字。
            """,
            
            "industry_overview": f"""
            请分析{industry_name}行业概况，包括：
            1. 行业定义和范围
            2. 产业链结构
            3. 发展历程和现状
            4. 行业特点和商业模式
            要求详细专业，约500-600字。
            """,
            
            "market_analysis": f"""
            请分析{industry_name}行业的市场情况，包括：
            1. 市场规模和增长趋势
            2. 细分市场分析
            3. 供需关系分析
            4. 价格走势分析
            要求数据支撑，约400-500字。
            """,
            
            "competitive_landscape": f"""
            请分析{industry_name}行业的竞争格局，包括：
            1. 主要参与者和市场份额
            2. 竞争优势和差异化
            3. 行业集中度分析
            4. 新进入者威胁
            要求客观全面，约400-500字。
            """,
            
            "policy_analysis": f"""
            请分析影响{industry_name}行业的政策因素，包括：
            1. 相关政策法规
            2. 政策支持和限制
            3. 政策变化趋势
            4. 政策影响评估
            要求准确及时，约300-400字。
            """,
            
            "investment_opportunities": f"""
            请分析{industry_name}行业的投资机会，包括：
            1. 投资热点和方向
            2. 投资价值评估
            3. 投资时机分析
            4. 投资建议
            要求实用性强，约400-500字。
            """,
            
            "risk_assessment": f"""
            请评估{industry_name}行业的投资风险，包括：
            1. 市场风险
            2. 政策风险
            3. 技术风险
            4. 竞争风险
            5. 风险防控建议
            要求全面客观，约400-500字。
            """,
            
            "conclusion": f"""
            请为{industry_name}行业研究报告撰写结论与建议，包括：
            1. 核心观点总结
            2. 投资评级建议
            3. 关键风险提示
            4. 后续关注要点
            要求简洁有力，约300-400字。
            """
        }
        
        prompt = section_prompts.get(section_type, f"请分析{industry_name}行业的{section_type}相关内容。")
        
        messages = [
            {"role": "system", "content": f"""你是一位专业的行业分析师，擅长撰写深度的行业研究报告。
            请基于以下数据背景进行分析：
            {data_context}
            
            要求：
            1. 分析专业深入，逻辑清晰
            2. 语言规范，符合研报标准
            3. 结合实际数据和市场情况
            4. 避免过于乐观或悲观的表述
            """},
            {"role": "user", "content": prompt}
        ]
        
        return self.analysis_agent.llm_client.create_completion(
            messages, max_tokens=1000, temperature=0.3
        )
    
    async def generate_industry_charts(self, industry_name: str, data_package: Dict[str, Any]) -> List[str]:
        """
        生成行业相关图表
        
        Args:
            industry_name: 行业名称
            data_package: 行业数据包
            
        Returns:
            生成的图表文件路径列表
        """
        logger.info(f"开始生成行业图表: {industry_name}")
        
        chart_paths = []
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        try:
            # 创建图表输出目录
            chart_dir = "./industry_reports/charts"
            os.makedirs(chart_dir, exist_ok=True)
            
            # 示例：行业市场结构饼图
            market_structure = {
                "龙头企业": 35.0,
                "二线企业": 25.0,
                "中小企业": 30.0,
                "其他": 10.0
            }
            
            pie_chart_path = os.path.join(chart_dir, f"{industry_name}_market_structure_{timestamp}.png")
            self.chart_generator.create_pie_chart(
                market_structure,
                title=f"{industry_name}行业市场结构",
                save_path=pie_chart_path
            )
            chart_paths.append(pie_chart_path)
            
            # 示例：宏观指标趋势图
            macro_data = data_package.get('macro_data', {})
            if 'GDP' in macro_data and not macro_data['GDP'].empty:
                gdp_data = macro_data['GDP']
                if 'quarter' in gdp_data.columns and 'gdp_yoy' in gdp_data.columns:
                    line_chart_path = os.path.join(chart_dir, f"gdp_trend_{timestamp}.png")
                    # 这里可以调用图表生成器创建趋势图
                    chart_paths.append(line_chart_path)
            
            logger.info(f"图表生成完成，共生成{len(chart_paths)}个图表")
            return chart_paths
            
        except Exception as e:
            logger.error(f"生成图表时出错: {e}")
            return []
    
    async def generate_report(self, industry_name: str, output_format: str = "html") -> str:
        """
        生成完整的行业研究报告
        
        Args:
            industry_name: 行业名称
            output_format: 输出格式 (html/docx)
            
        Returns:
            生成的报告文件路径
        """
        logger.info(f"开始生成{industry_name}行业研究报告")
        
        try:
            # 1. 收集数据
            print(f"📊 正在收集{industry_name}行业数据...")
            data_package = await self.collect_industry_data(industry_name)
            
            if "error" in data_package:
                raise Exception(f"数据收集失败: {data_package['error']}")
            
            # 2. 进行分析
            print(f"🔍 正在分析{industry_name}行业...")
            analysis_results = await self.analyze_industry(industry_name, data_package)
            
            if "error" in analysis_results:
                raise Exception(f"行业分析失败: {analysis_results['error']}")
            
            # 3. 生成图表
            print(f"📈 正在生成{industry_name}行业图表...")
            chart_paths = await self.generate_industry_charts(industry_name, data_package)
            
            # 4. 生成报告文件
            print(f"📝 正在生成{industry_name}行业报告...")
            if output_format.lower() == "docx":
                report_path = self._generate_docx_report(industry_name, analysis_results, chart_paths)
            else:
                report_path = self._generate_html_report(industry_name, analysis_results, chart_paths)
            
            logger.info(f"行业研究报告生成完成: {report_path}")
            return report_path
            
        except Exception as e:
            logger.error(f"生成报告时出错: {e}")
            raise
    
    def _generate_docx_report(self, industry_name: str, analysis_results: Dict[str, str], chart_paths: List[str]) -> str:
        """生成DOCX格式报告"""
        output_dir = "./industry_reports"
        os.makedirs(output_dir, exist_ok=True)
        
        doc = Document()
        
        # 标题
        title = doc.add_heading(f'{industry_name}行业研究报告', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 元数据
        doc.add_paragraph(f"生成时间: {datetime.now().strftime('%Y年%m月%d日')}")
        doc.add_paragraph(f"分析师: AI智能分析系统")
        doc.add_paragraph("")
        
        # 添加各个章节
        section_titles = {
            "executive_summary": "执行摘要",
            "industry_overview": "行业概况", 
            "market_analysis": "市场分析",
            "competitive_landscape": "竞争格局",
            "policy_analysis": "政策分析",
            "investment_opportunities": "投资机会",
            "risk_assessment": "风险评估",
            "conclusion": "结论与建议"
        }
        
        for section_key, section_title in section_titles.items():
            if section_key in analysis_results:
                doc.add_heading(section_title, level=1)
                doc.add_paragraph(analysis_results[section_key])
                doc.add_paragraph("")
        
        # 保存文件
        filename = "Industry_Research_Report.docx"
        report_path = os.path.join(output_dir, filename)
        doc.save(report_path)
        
        return report_path
    
    def _generate_html_report(self, industry_name: str, analysis_results: Dict[str, str], chart_paths: List[str]) -> str:
        """生成HTML格式报告"""
        output_dir = "./industry_reports"
        os.makedirs(output_dir, exist_ok=True)
        
        # 简单的HTML模板
        html_content = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{industry_name}行业研究报告</title>
            <style>
                body {{ font-family: 'Microsoft YaHei', sans-serif; margin: 40px; line-height: 1.6; }}
                h1 {{ color: #2c3e50; text-align: center; border-bottom: 2px solid #3498db; padding-bottom: 10px; }}
                h2 {{ color: #34495e; margin-top: 30px; }}
                .metadata {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 30px; }}
                .section {{ margin-bottom: 30px; }}
                .chart {{ text-align: center; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <h1>{industry_name}行业研究报告</h1>
            
            <div class="metadata">
                <p><strong>生成时间:</strong> {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
                <p><strong>分析师:</strong> AI智能分析系统</p>
                <p><strong>报告类型:</strong> 行业研究报告</p>
            </div>
        """
        
        # 添加各个章节
        section_titles = {
            "executive_summary": "执行摘要",
            "industry_overview": "行业概况",
            "market_analysis": "市场分析", 
            "competitive_landscape": "竞争格局",
            "policy_analysis": "政策分析",
            "investment_opportunities": "投资机会",
            "risk_assessment": "风险评估",
            "conclusion": "结论与建议"
        }
        
        for section_key, section_title in section_titles.items():
            if section_key in analysis_results:
                html_content += f"""
                <div class="section">
                    <h2>{section_title}</h2>
                    <p>{analysis_results[section_key].replace('\n', '<br>')}</p>
                </div>
                """
        
        # 添加图表
        if chart_paths:
            html_content += '<div class="section"><h2>图表分析</h2>'
            for chart_path in chart_paths:
                if os.path.exists(chart_path):
                    chart_name = os.path.basename(chart_path)
                    html_content += f'<div class="chart"><img src="charts/{chart_name}" alt="图表" style="max-width: 100%;"></div>'
            html_content += '</div>'
        
        html_content += """
            <div class="section">
                <h2>免责声明</h2>
                <p>本报告由AI系统自动生成，仅供参考，不构成投资建议。投资有风险，入市需谨慎。</p>
            </div>
        </body>
        </html>
        """
        
        # 保存文件
        filename = "Industry_Research_Report.html"
        report_path = os.path.join(output_dir, filename)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return report_path

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='行业研究报告生成器')
    parser.add_argument('--industry_name', '-i', type=str, required=True, 
                       help='行业名称 (例如: 人工智能, 新能源汽车)')
    parser.add_argument('--format', '-f', type=str, default='docx',
                       choices=['html', 'docx'], help='输出格式')
    
    args = parser.parse_args()
    
    print("🏭 行业研究报告生成器")
    print("=" * 50)
    print(f"📋 目标行业: {args.industry_name}")
    print(f"📄 输出格式: {args.format}")
    print()
    
    try:
        generator = IndustryResearchGenerator()
        
        # 验证行业名称
        if args.industry_name not in generator.industry_mapping:
            print(f"⚠️  行业名称 '{args.industry_name}' 可能不在预设列表中")
            print("支持的行业包括:", ", ".join(generator.industry_mapping.keys()))
            print("将尝试继续生成...")
            print()
        
        # 生成报告
        report_path = await generator.generate_report(args.industry_name, args.format)
        
        print("✅ 报告生成完成!")
        print(f"📁 报告路径: {report_path}")
        print(f"📊 报告格式: {args.format.upper()}")
        
        if os.path.exists(report_path):
            file_size = os.path.getsize(report_path) / 1024  # KB
            print(f"📏 文件大小: {file_size:.1f} KB")
        
        print("\n🎯 报告已保存到 industry_reports 目录")
        
    except KeyboardInterrupt:
        print("\n⏹️  操作被用户取消")
    except Exception as e:
        print(f"❌ 生成报告时出错: {e}")
        logger.error(f"报告生成失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
