# FinAgentX - 智能金融研报生成系统

## 🎯 项目简介

FinAgent 是一个基于大语言模型的智能金融研报生成系统，能够自动收集、分析和生成专业的公司研究报告。系统集成了数据爬取、财务分析、市场观点收集、图表生成等多个功能模块，为投资者和分析师提供全面、准确的投资决策支持。

## ✨ 核心功能

### 📊 数据收集与分析
- **财务数据爬取**: 自动获取公司基本信息、财务数据、股价信息
- **市场观点收集**: 从股吧等平台收集投资者观点和市场情绪
- **新闻资讯整合**: 收集相关新闻和行业动态

### 📈 智能分析
- **财务分析**: 基于LLM的深度财务数据分析
- **技术分析**: 股价走势和技术指标分析
- **市场情绪分析**: 投资者观点和市场情绪量化分析

### 📋 报告生成
- **专业研报**: 生成结构化的投资研究报告
- **可视化图表**: 自动生成财务图表和技术分析图
- **多格式输出**: 支持Markdown、PDF等多种格式

## 🚀 快速开始

### 环境要求
- Python 3.8+
- 所需依赖包（见requirements.txt）

### 安装步骤

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **配置设置**
```bash
# 编辑配置文件，填入API密钥等信息
# 配置文件位于 config.py
```

### 基本使用（极简接口）

```bash
# 使用公司名称生成研报
python run_company_research_report.py 4Paradigm
python run_company_research_report.py 平安银行
python run_company_research_report.py 腾讯控股

# 使用股票代码生成研报
python run_company_research_report.py 06682.HK
python run_company_research_report.py 000001.SZ
python run_company_research_report.py 00700.HK

# 跳过某些步骤
python run_company_research_report.py 4Paradigm --skip_steps 2 5
```

### 支持的输入格式
- ✅ **公司名称**: 4Paradigm, 平安银行, 腾讯控股
- ✅ **港股代码**: 06682.HK, 00700.HK, 09988.HK
- ✅ **A股代码**: 000001.SZ, 600036.SH, 600519.SH
- ✅ **美股代码**: AAPL, MSFT, TSLA

## 📁 核心文件结构

```
FinAgentX/
├── run_company_research_report.py  # 🚀 主程序入口（单参数智能接口）
├── crawler.py                      # 📊 数据爬取模块
├── crawler_viewpoint.py           # 💬 观点爬取模块
├── visualize_report.py            # 📈 图表生成模块
├── financial_agent.py             # 💰 财务分析模块
├── guba_viewpoint_agent.py        # 🗣️ 股吧观点分析模块
├── research_report_generator.py   # 📋 研报生成模块
├── config.py                      # ⚙️ 配置文件
└── README.md                      # 📖 项目说明
```

## 🔧 核心模块说明

### 1. 主程序 (run_company_research_report.py)
- **单参数智能接口**: 只需一个参数即可运行
- **智能识别**: 自动区分公司名称和股票代码
- **流水线管理**: 按顺序执行6个步骤的完整研报生成流程
- **编码兼容**: 彻底解决中文编码问题，支持各种系统环境

### 2. 数据爬取 (crawler.py)
- **基础数据收集**: 爬取公司基本信息、财务数据
- **股价信息**: 获取实时和历史股价数据
- **新闻资讯**: 收集相关新闻和公告信息

### 3. 观点爬取 (crawler_viewpoint.py)
- **投资者观点**: 从股吧等平台收集投资者讨论
- **市场情绪**: 提取市场情绪和热点话题
- **数据清洗**: 观点数据的预处理和筛选

### 4. 图表生成 (visualize_report.py)
- **财务图表**: 生成专业的财务数据可视化图表
- **技术分析图**: 创建股价走势和技术指标图表
- **多样化图表**: 支持柱状图、折线图、饼图等多种类型

### 5. 财务分析 (financial_agent.py)
- **LLM深度分析**: 基于大语言模型的财务数据深度分析
- **关键指标计算**: 自动计算和分析关键财务指标
- **财务健康度**: 生成综合财务健康度评估

### 6. 观点分析 (guba_viewpoint_agent.py)
- **情绪分析**: 分析投资者观点和市场情绪
- **观点提取**: 提取关键观点和争议点
- **情绪量化**: 将市场情绪转化为量化指标

### 7. 研报生成 (research_report_generator.py)
- **报告整合**: 整合所有分析结果生成完整研报
- **结构化输出**: 生成结构化的投资研究报告
- **多格式支持**: 支持Markdown、PDF等多种输出格式

### 8. 配置管理 (config.py)
- **统一配置**: 统一管理系统所有配置参数
- **LLM设置**: API密钥、模型选择、请求参数等
- **模块化管理**: 各模块独立配置，便于维护

## ⚙️ 配置说明

### config.py 配置文件
```python
# LLM配置
class LLMConfig:
    model = "deepseek/deepseek-r1-0528:free"
    base_url = "https://openrouter.ai/api/v1"
    api_key = "your_api_key_here"
    temperature = 0.1
    max_tokens = 4000

# 数据源配置
class DataConfig:
    enable_crawler = True
    enable_viewpoint = True
    timeout = 30
    retry_times = 3

# 输出配置
class OutputConfig:
    report_format = "markdown"
    chart_format = "png"
    output_dir = "outputs"
```

### 必需配置项
1. **API密钥**: 在config.py中设置OpenRouter API密钥
2. **模型选择**: 推荐使用deepseek/deepseek-r1-0528:free（免费且效果好）
3. **输出目录**: 确保有写入权限

## 📊 6步研报生成流程

系统按以下6个步骤自动生成完整研报：

### 步骤1: 数据爬取 (crawler.py)
- 🔍 **公司基础信息**: 公司名称、行业分类、主营业务
- 📈 **财务数据**: 营收、利润、资产负债等核心财务指标
- 📰 **新闻资讯**: 最新公司新闻、公告、行业动态
- ⏱️ **预计耗时**: 30-60秒

### 步骤2: 观点爬取 (crawler_viewpoint.py)
- 💬 **投资者观点**: 从股吧等平台收集投资者讨论
- 😊 **市场情绪**: 提取正面、负面、中性情绪
- 🔥 **热点话题**: 识别当前讨论热点和关注焦点
- ⏱️ **预计耗时**: 45-90秒

### 步骤3: 图表生成 (visualize_report.py)
- 📊 **财务图表**: 营收利润趋势、财务指标对比
- 📈 **股价图表**: 股价走势、技术指标分析
- 🎯 **专业图表**: 8类专业财务分析图表
- ⏱️ **预计耗时**: 20-40秒

### 步骤4: 财务分析 (financial_agent.py)
- 🧮 **深度分析**: 基于LLM的财务数据深度解读
- 📋 **指标计算**: 盈利能力、成长性、财务健康度
- 💡 **专业见解**: AI生成的专业财务分析观点
- ⏱️ **预计耗时**: 60-120秒

### 步骤5: 观点分析 (guba_viewpoint_agent.py)
- 🎭 **情绪分析**: 量化投资者情绪和市场预期
- 🔍 **观点提取**: 提取关键投资观点和争议点
- 📊 **情绪指标**: 生成市场情绪量化指标
- ⏱️ **预计耗时**: 45-90秒

### 步骤6: 研报生成 (research_report_generator.py)
- 📝 **报告整合**: 整合所有分析结果
- 📋 **结构化输出**: 生成专业的投资研究报告
- 🎯 **投资建议**: 基于综合分析的投资建议和风险提示
- ⏱️ **预计耗时**: 90-180秒

### 总耗时: 约5-10分钟

## 📈 生成的研报内容

### 完整研报包含：
1. **📋 执行摘要**: 关键发现和投资建议概览
2. **🏢 公司概况**: 基本信息、业务描述、行业地位
3. **💰 财务分析**: 关键财务指标、盈利能力、财务健康度
4. **📊 技术分析**: 股价走势、技术指标、支撑阻力位
5. **💬 市场观点**: 投资者情绪、热点话题、争议分析
6. **🎯 投资建议**: 综合评级、目标价位、风险提示

### 专业图表包含：
- 📊 **财务数据趋势图**: 营收、利润、资产变化趋势
- 📈 **股价走势图**: 技术分析和关键价位标注
- 🎯 **财务指标对比图**: 与行业平均水平对比
- 😊 **市场情绪分析图**: 投资者情绪量化展示

## 🛠️ 高级功能

### 跳过特定步骤
```bash
# 跳过观点爬取和图表生成
python run_company_research_report.py 4Paradigm --skip_steps 2 3

# 只运行财务分析
python run_company_research_report.py 平安银行 --skip_steps 1 2 3 5 6
```

### 自定义超时时间
```bash
# 设置单个步骤超时为10分钟
python run_company_research_report.py 腾讯控股 --timeout 600
```

### 批量处理示例
```python
# 批量生成多个公司的研报
companies = ["4Paradigm", "06682.HK", "平安银行", "000001.SZ"]

for company in companies:
    os.system(f"python run_company_research_report.py {company}")
```

## 🔍 故障排除

### 常见问题解决

1. **编码错误**
   - ✅ 已彻底解决：系统使用二进制模式处理，支持所有编码
   - 支持UTF-8、GBK、GB2312等各种编码格式

2. **API密钥错误**
   - 检查config.py中的API密钥配置
   - 确认OpenRouter账户有足够额度

3. **网络连接问题**
   - 检查网络连接和防火墙设置
   - 确认能访问OpenRouter API

4. **数据爬取失败**
   - 检查目标网站是否可访问
   - 系统会自动重试，通常能自动恢复

### 系统特点
- ✅ **编码兼容**: 完美支持中文Windows环境
- ✅ **错误恢复**: 单步失败不影响整体流程
- ✅ **详细日志**: 提供详细的执行状态和错误信息
- ✅ **智能识别**: 自动识别公司名称和股票代码

## 💡 使用技巧

### 最佳实践
1. **首次使用**: 建议先用知名公司测试，如`python run_company_research_report.py 腾讯控股`
2. **网络环境**: 确保网络稳定，避免在网络不稳定时运行
3. **API额度**: 注意OpenRouter API使用额度，避免超限
4. **存储空间**: 确保有足够磁盘空间存储生成的报告和图表

### 输出文件说明
- **执行报告**: `execution_report_[股票代码]_[时间戳].md`
- **研报文件**: 各模块会生成对应的分析报告
- **图表文件**: 保存在相应的图表目录中
- **日志文件**: 详细的执行日志便于问题排查

## 🎯 项目优势

### 🚀 **技术优势**
- **单参数接口**: 极简使用，一个参数搞定
- **智能识别**: 自动区分公司名称和股票代码
- **编码兼容**: 彻底解决中文编码问题
- **模块化设计**: 8个核心模块，功能清晰

### 📊 **功能优势**
- **全流程自动化**: 从数据收集到报告生成全自动
- **多维度分析**: 财务、技术、情绪多角度分析
- **专业图表**: 8类专业财务分析图表
- **LLM驱动**: 基于大语言模型的深度分析

### 🛡️ **稳定性优势**
- **错误容错**: 单步失败不影响整体流程
- **编码处理**: 支持各种编码格式，不会因编码问题中断
- **重试机制**: 网络问题自动重试
- **详细日志**: 完善的日志系统便于问题定位

### 💼 **商业价值**
- **投资决策支持**: 为投资者提供全面的决策依据
- **效率提升**: 自动化生成专业研报，大幅提升效率
- **成本节约**: 减少人工分析成本
- **标准化输出**: 确保分析报告的专业性和一致性

## 📞 联系与支持

### 使用帮助
- 📖 **详细文档**: 参考本README和代码注释
- 🐛 **问题反馈**: 遇到问题请提供详细的错误日志
- 💡 **功能建议**: 欢迎提出改进建议

### 系统要求
- **操作系统**: Windows 10+, Linux, macOS
- **Python版本**: 3.8+
- **内存要求**: 建议4GB以上
- **网络要求**: 稳定的互联网连接

---

## 🎉 开始使用

现在就开始使用FinAgentX生成您的第一份智能研报：

```bash
# 最简单的使用方式
python run_company_research_report.py 4Paradigm
```

**FinAgentX** - 让投资分析更智能，让决策更精准！

---

*最后更新: 2025年7月24日*
*版本: v2.0 - 单参数智能接口版*
4. **盈利能力趋势** - 盈利质量变化
5. **利润表结构分析** - 收入成本结构
6. **现金流分析** - 现金流状况
7. **估值分析** - 估值水平变化
8. **综合评分雷达图** - 多维度综合评估

## 🔍 示例输出

以商汤-W (00020.HK)为例：

```markdown
# 商汤-W (00020.HK) 财务分析报告

**报告生成时间**: 2025-07-14 18:26:42  
**分析期间**: 2022-12-31 至 2024-12-31  
**数据来源**: 主要财务指标

## 📊 执行摘要
- 营业收入：37.721亿港元（2024年恢复增长+10.75%）
- 归母净利润：-42.784亿港元（亏损收窄33.57%）
- 净利润率：-114.17%（仍深度亏损但有改善）
- 投资建议：谨慎观望，关注盈利拐点

## 💡 投资建议
- **投资策略**: 中性偏谨慎
- **关键风险**: 持续亏损、现金流压力
- **关注指标**: 收入增长率、亏损收窄程度
```

## ⚠️ 注意事项

1. **数据质量**: 确保CSV数据完整准确
2. **网络连接**: 需要稳定的网络连接访问OpenRouter API
3. **API限制**: 免费模型可能有调用频率限制
4. **分析局限**: 分析基于历史数据，不构成投资建议

## 🛠️ 自定义配置

可以通过修改`config.py`来调整：
- LLM模型和参数
- 分析期数和输出格式
- API密钥和超时设置

## 📞 技术支持

如遇问题，请检查：
1. 数据文件格式是否正确
2. 网络连接是否正常
3. API密钥是否有效
4. 依赖包是否完整安装

---

*本系统由FinAgentX开发，基于DeepSeek R1模型提供智能财务分析服务*
