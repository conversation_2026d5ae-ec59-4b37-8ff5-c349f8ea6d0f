# FinAgentX Docker构建忽略文件

# Git相关
.git/
.gitignore
.gitattributes
.github/

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/
.conda/

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~
.sublime-*

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# 日志文件
*.log
logs/
*.log.*

# 临时文件
*.tmp
*.temp
.cache/
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# 文档（保留README.md）
docs/
*.md
!README.md
!docker_image/README.md

# 配置文件（可能包含敏感信息）
.env
.env.local
.env.*.local
config.local.py

# 运行时生成的数据文件
*.csv
*.xlsx
*.xls
*.json
*.png
*.jpg
*.jpeg
*.pdf
*.docx

# 输出目录（运行时创建）
outputs/
reports/
analysis_reports/
report_images/
data/
logs/

# 测试和开发文件
test_*.py
*_test.py
tests/
test_data/
*.test

# Docker相关（避免递归复制）
Dockerfile*
docker-compose*
.dockerignore

# 系统检查和报告文件
*_report.md
*_report.json
system_check_*
execution_report_*
mermaid_diagrams_report.md
readme_enhancement_report.md
company_name_fix_report.md
guba_url_fix_report.md
interface_usage_guide.md

# 开发工具
fix_unicode.py
test_guba_url.py
test_interfaces.py
test_simple.py
check_system.py
