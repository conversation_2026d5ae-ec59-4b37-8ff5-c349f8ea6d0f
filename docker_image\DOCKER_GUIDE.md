# FinAgentX Docker 使用指南

## 🐳 Docker镜像构建与使用

### 📋 镜像信息
- **镜像名称**: finagentx
- **版本**: v2.1
- **基础镜像**: python:3.11-slim
- **支持架构**: amd64, arm64

### 🔨 构建镜像

#### 1. 基础构建
```bash
# 在项目根目录执行
cd docker_image
docker build -t finagentx:latest .
```

#### 2. 多平台构建
```bash
# 构建支持多平台的镜像
docker buildx build --platform linux/amd64,linux/arm64 -t finagentx:latest .
```

#### 3. 带标签构建
```bash
# 构建带版本标签的镜像
docker build -t finagentx:v2.1 -t finagentx:latest .
```

### 🚀 运行容器

#### 1. 简化格式运行
```bash
# 使用公司名称
docker run --rm finagentx 商汤科技

# 使用股票代码
docker run --rm finagentx 00020.HK

# 跳过特定步骤
docker run --rm finagentx 商汤科技 --skip_steps 2 5
```

#### 2. 完整格式运行
```bash
# 明确指定公司信息
docker run --rm finagentx --company_name 商汤科技 --company_code 00020.HK

# 带超时设置
docker run --rm finagentx --company_name 商汤科技 --company_code 00020.HK --timeout 1800
```

#### 3. 挂载数据卷
```bash
# 挂载输出目录到宿主机
docker run --rm -v $(pwd)/outputs:/app/outputs finagentx 商汤科技

# 挂载配置文件
docker run --rm -v $(pwd)/config.py:/app/config.py finagentx 商汤科技
```

#### 4. 环境变量配置
```bash
# 设置API密钥
docker run --rm -e OPENROUTER_API_KEY=your_api_key finagentx 商汤科技

# 设置默认公司信息
docker run --rm -e COMPANY_NAME=腾讯控股 -e COMPANY_CODE=00700.HK finagentx
```

### 🔧 高级用法

#### 1. 交互式运行
```bash
# 进入容器交互模式
docker run -it --rm finagentx bash

# 在容器内运行
python run_company_research_report.py 商汤科技
```

#### 2. 后台运行
```bash
# 后台运行并保存日志
docker run -d --name finagentx-job finagentx 商汤科技 > finagentx.log 2>&1

# 查看运行状态
docker ps
docker logs finagentx-job
```

#### 3. 资源限制
```bash
# 限制内存和CPU使用
docker run --rm --memory=4g --cpus=2 finagentx 商汤科技
```

### 📊 Docker Compose 配置

创建 `docker-compose.yml` 文件：

```yaml
version: '3.8'

services:
  finagentx:
    build: .
    image: finagentx:latest
    container_name: finagentx-service
    environment:
      - COMPANY_NAME=商汤科技
      - COMPANY_CODE=00020.HK
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
    volumes:
      - ./outputs:/app/outputs
      - ./logs:/app/logs
      - ./config.py:/app/config.py:ro
    command: ["商汤科技", "--skip_steps", "2"]
    restart: unless-stopped
    mem_limit: 4g
    cpus: 2
```

使用Docker Compose：
```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 🔍 故障排除

#### 1. 构建问题
```bash
# 清理构建缓存
docker builder prune

# 强制重新构建
docker build --no-cache -t finagentx:latest .
```

#### 2. 运行问题
```bash
# 检查容器状态
docker ps -a

# 查看容器日志
docker logs container_name

# 进入容器调试
docker exec -it container_name bash
```

#### 3. 权限问题
```bash
# 以root用户运行（不推荐）
docker run --rm --user root finagentx 商汤科技
```

### 📈 性能优化

#### 1. 镜像优化
- 使用多阶段构建减小镜像大小
- 合并RUN指令减少层数
- 清理不必要的文件和缓存

#### 2. 运行优化
- 合理设置内存和CPU限制
- 使用数据卷避免重复下载
- 配置合适的健康检查间隔

### 🛡️ 安全最佳实践

#### 1. 用户权限
- 容器内使用非root用户运行
- 只读挂载配置文件
- 限制容器权限

#### 2. 网络安全
- 只暴露必要的端口
- 使用私有网络
- 配置防火墙规则

#### 3. 数据安全
- 不在镜像中包含敏感信息
- 使用环境变量传递密钥
- 定期更新基础镜像

### 📋 常用命令

```bash
# 查看镜像信息
docker images finagentx

# 查看镜像历史
docker history finagentx:latest

# 导出镜像
docker save finagentx:latest | gzip > finagentx.tar.gz

# 导入镜像
gunzip -c finagentx.tar.gz | docker load

# 推送到仓库
docker tag finagentx:latest your-registry/finagentx:latest
docker push your-registry/finagentx:latest
```

### 🎯 使用示例

#### 批量处理多个公司
```bash
#!/bin/bash
companies=("商汤科技" "腾讯控股" "阿里巴巴" "平安银行")

for company in "${companies[@]}"; do
    echo "处理公司: $company"
    docker run --rm -v $(pwd)/outputs:/app/outputs finagentx "$company"
done
```

#### 定时任务
```bash
# 添加到crontab
0 9 * * 1 docker run --rm -v /path/to/outputs:/app/outputs finagentx 商汤科技
```

---

**更新时间**: 2025-07-29  
**Docker版本**: 20.10+  
**支持平台**: Linux, macOS, Windows
