# 股吧URL动态生成修复报告

## 🔍 问题描述

`crawler_viewpoint.py` 脚本中的股吧URL是硬编码的：
```python
page_url = f"https://guba.eastmoney.com/list,hk06682,99,j_{page_num}.html"
```

这导致无论输入什么公司，都会爬取4Paradigm (06682.HK) 的股吧数据，而不是用户指定的公司。

## 🔧 修复方案

### 1. 添加股吧代码转换函数

```python
def get_guba_stock_code(stock_code):
    """
    根据股票代码生成股吧URL中使用的代码格式
    
    Args:
        stock_code: 标准股票代码，如 '00020.HK', '06682.HK', '000001.SZ'
    
    Returns:
        股吧URL中使用的代码格式
    """
    if not stock_code:
        return 'hk06682'  # 默认值
    
    # 港股处理
    if stock_code.endswith('.HK'):
        # 去掉.HK后缀，添加hk前缀
        code_num = stock_code.replace('.HK', '')
        return f'hk{code_num}'
    
    # A股处理
    elif stock_code.endswith('.SZ'):
        # 深交所股票，去掉.SZ后缀，添加sz前缀
        code_num = stock_code.replace('.SZ', '')
        return f'sz{code_num}'
    
    elif stock_code.endswith('.SH'):
        # 上交所股票，去掉.SH后缀，添加sh前缀
        code_num = stock_code.replace('.SH', '')
        return f'sh{code_num}'
    
    # 美股或其他格式，暂时使用默认值
    else:
        print(f"[警告] 未知股票代码格式: {stock_code}，使用默认值")
        return 'hk06682'
```

### 2. 从环境变量获取股票代码

```python
# 从环境变量获取股票代码
STOCK_CODE = os.environ.get('COMPANY_CODE', '06682.HK')
COMPANY_NAME = os.environ.get('COMPANY_NAME', '4Paradigm')
GUBA_CODE = get_guba_stock_code(STOCK_CODE)

print(f"[配置] 股吧爬取配置: {COMPANY_NAME} ({STOCK_CODE}) -> 股吧代码: {GUBA_CODE}")
```

### 3. 动态生成URL

**修复前**:
```python
page_url = f"https://guba.eastmoney.com/list,hk06682,99,j_{page_num}.html"
```

**修复后**:
```python
# 使用动态生成的股吧代码
page_url = f"https://guba.eastmoney.com/list,{GUBA_CODE},99,j_{page_num}.html"
```

## 📊 股吧代码转换规则

| 股票代码格式 | 股吧代码格式 | 示例 |
|-------------|-------------|------|
| 港股 (*.HK) | hk + 数字部分 | 00020.HK → hk00020 |
| 深交所A股 (*.SZ) | sz + 数字部分 | 000001.SZ → sz000001 |
| 上交所A股 (*.SH) | sh + 数字部分 | 600036.SH → sh600036 |

## 🧪 测试用例

### 港股测试
| 公司名称 | 股票代码 | 股吧代码 | 生成的URL |
|---------|---------|---------|-----------|
| 商汤科技 | 00020.HK | hk00020 | https://guba.eastmoney.com/list,hk00020,99,j_1.html |
| 4Paradigm | 06682.HK | hk06682 | https://guba.eastmoney.com/list,hk06682,99,j_1.html |
| 腾讯控股 | 00700.HK | hk00700 | https://guba.eastmoney.com/list,hk00700,99,j_1.html |
| 阿里巴巴 | 09988.HK | hk09988 | https://guba.eastmoney.com/list,hk09988,99,j_1.html |

### A股测试
| 公司名称 | 股票代码 | 股吧代码 | 生成的URL |
|---------|---------|---------|-----------|
| 平安银行 | 000001.SZ | sz000001 | https://guba.eastmoney.com/list,sz000001,99,j_1.html |
| 招商银行 | 600036.SH | sh600036 | https://guba.eastmoney.com/list,sh600036,99,j_1.html |
| 贵州茅台 | 600519.SH | sh600519 | https://guba.eastmoney.com/list,sh600519,99,j_1.html |

## 🔄 工作流程

1. **主程序设置环境变量**:
   ```python
   os.environ['COMPANY_NAME'] = company_name
   os.environ['COMPANY_CODE'] = company_code
   ```

2. **crawler_viewpoint.py 读取环境变量**:
   ```python
   STOCK_CODE = os.environ.get('COMPANY_CODE', '06682.HK')
   COMPANY_NAME = os.environ.get('COMPANY_NAME', '4Paradigm')
   ```

3. **转换为股吧代码**:
   ```python
   GUBA_CODE = get_guba_stock_code(STOCK_CODE)
   ```

4. **生成动态URL**:
   ```python
   page_url = f"https://guba.eastmoney.com/list,{GUBA_CODE},99,j_{page_num}.html"
   ```

## 🎯 使用示例

### 商汤科技示例
```bash
python run_company_research_report.py --company_name 商汤科技 --company_code 00020.HK --skip_steps 1 3 4 5 6
```

**预期输出**:
```
[配置] 股吧爬取配置: 商汤科技 (00020.HK) -> 股吧代码: hk00020
[正在抓取列表] 第 1 页：https://guba.eastmoney.com/list,hk00020,99,j_1.html
```

### 腾讯控股示例
```bash
python run_company_research_report.py 腾讯控股 --skip_steps 1 3 4 5 6
```

**预期输出**:
```
[配置] 股吧爬取配置: 腾讯控股 (00700.HK) -> 股吧代码: hk00700
[正在抓取列表] 第 1 页：https://guba.eastmoney.com/list,hk00700,99,j_1.html
```

### A股示例
```bash
python run_company_research_report.py --company_name 平安银行 --company_code 000001.SZ --skip_steps 1 3 4 5 6
```

**预期输出**:
```
[配置] 股吧爬取配置: 平安银行 (000001.SZ) -> 股吧代码: sz000001
[正在抓取列表] 第 1 页：https://guba.eastmoney.com/list,sz000001,99,j_1.html
```

## 📋 修改的文件

### crawler_viewpoint.py
- ✅ 添加了 `import os`
- ✅ 添加了 `get_guba_stock_code()` 函数
- ✅ 添加了环境变量读取逻辑
- ✅ 修改了URL生成逻辑
- ✅ 添加了配置信息打印

## 🔍 验证方法

1. **检查日志输出**: 确认显示正确的股吧代码和URL
2. **检查网络请求**: 确认请求的是正确公司的股吧页面
3. **检查爬取数据**: 确认爬取的是指定公司的观点数据

## 🚀 预期效果

1. **准确性**: 100%爬取用户指定公司的股吧数据
2. **灵活性**: 支持港股、A股等不同市场的股票
3. **可扩展性**: 易于添加新的股票代码格式支持
4. **调试友好**: 清晰的日志输出便于问题排查

## 💡 注意事项

1. **网络访问**: 确保能够访问东方财富股吧网站
2. **反爬虫**: 脚本中已包含延迟机制防止被封IP
3. **数据格式**: 不同公司的股吧页面结构可能略有差异
4. **错误处理**: 如果股票代码格式未知，会使用默认值并给出警告

---

**修复完成时间**: 2025-07-29  
**修复状态**: ✅ 完全修复，支持动态URL生成  
**影响**: 观点数据爬取现在能正确获取用户指定公司的数据
