"""
Configuration file for the Financial Research Report Agent System
"""
import os
from typing import Dict, Any

# API Keys
TUSHARE_TOKEN = "4efdf5af6c99fc973cad5e5e86b026a2ce23417f5ef371d99cfec1ae"
OPENROUTER_API_KEY = "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"

# OpenRouter Configuration
OPENROUTER_CONFIG = {
    "base_url": "https://openrouter.ai/api/v1",
    "model": "deepseek/deepseek-r1-0528:free",
    "api_key": OPENROUTER_API_KEY,
    "headers": {
        "HTTP-Referer": "https://github.com/financial-agent",
        "X-Title": "Financial Research Agent"
    }
}

# Data Sources Configuration
DATA_SOURCES = {
    "tushare": {
        "token": TUSHARE_TOKEN,
        "base_url": "http://api.tushare.pro"
    },
    "news_sources": [
        "https://finance.sina.com.cn/",
        "https://www.cls.cn/",
        "https://www.stcn.com/"
    ],
    "exchange_urls": {
        "sse": "http://www.sse.com.cn/",
        "szse": "http://www.szse.cn/",
        "hkex": "https://www.hkex.com.hk/"
    }
}

# Report Configuration
REPORT_CONFIG = {
    "output_dir": "./reports",
    "template_dir": "./templates",
    "chart_dir": "./charts",
    "supported_formats": ["pdf", "html", "docx"],
    "default_format": "html"
}

# Agent System Configuration
AGENT_CONFIG = {
    "max_retries": 3,
    "timeout": 30,
    "concurrent_agents": 5,
    "memory_size": 1000,
    "enable_feedback_loop": True
}

# Report Types and Requirements
REPORT_TYPES = {
    "macro": {
        "name": "宏观经济/策略研报",
        "required_indicators": ["GDP", "CPI", "利率", "汇率", "货币供应量", "进出口"],
        "analysis_depth": "deep",
        "forecast_period": 12  # months
    },
    "industry": {
        "name": "行业/子行业研报", 
        "required_data": ["行业规模", "竞争格局", "产业链分析", "政策影响"],
        "analysis_depth": "medium",
        "forecast_period": 36  # months
    },
    "company": {
        "name": "公司/个股研报",
        "required_statements": ["资产负债表", "利润表", "现金流量表"],
        "analysis_depth": "detailed",
        "forecast_period": 24  # months
    }
}

# Financial Ratios for Analysis
FINANCIAL_RATIOS = {
    "profitability": ["ROE", "ROA", "净利润率", "毛利率", "EBITDA率"],
    "liquidity": ["流动比率", "速动比率", "现金比率"],
    "leverage": ["资产负债率", "股东权益比率", "利息覆盖倍数"],
    "efficiency": ["总资产周转率", "存货周转率", "应收账款周转率"],
    "valuation": ["P/E", "P/B", "EV/EBITDA", "PEG"]
}

# Chart Configuration
CHART_CONFIG = {
    "style": "seaborn-v0_8",
    "figsize": (12, 8),
    "dpi": 300,
    "colors": ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd"],
    "font_family": "SimHei",  # For Chinese characters
    "chart_types": {
        "line": "时间序列图",
        "bar": "柱状图", 
        "pie": "饼图",
        "scatter": "散点图",
        "heatmap": "热力图"
    }
}

def get_config() -> Dict[str, Any]:
    """Get all configuration as a dictionary"""
    return {
        "tushare_token": TUSHARE_TOKEN,
        "openrouter": OPENROUTER_CONFIG,
        "data_sources": DATA_SOURCES,
        "reports": REPORT_CONFIG,
        "agents": AGENT_CONFIG,
        "report_types": REPORT_TYPES,
        "financial_ratios": FINANCIAL_RATIOS,
        "charts": CHART_CONFIG
    }