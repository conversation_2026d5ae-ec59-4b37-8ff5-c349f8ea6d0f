# 核心依赖
requests>=2.28.0
pandas>=1.5.0
numpy>=1.21.0
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.10.0

# 网页爬取
beautifulsoup4>=4.11.0
lxml>=4.9.0
selenium>=4.5.0

# AI/LLM相关
openai>=1.0.0
anthropic>=0.3.0

# 数据处理
openpyxl>=3.0.0
xlrd>=2.0.0
python-dateutil>=2.8.0

# 图像处理
Pillow>=9.0.0

# 异步处理
aiohttp>=3.8.0
asyncio-throttle>=1.0.0

# 配置管理
python-dotenv>=0.19.0
pydantic>=1.10.0

# 日志和调试
loguru>=0.6.0

# 文档生成
markdown>=3.4.0
reportlab>=3.6.0

# 数据可视化增强
kaleido>=0.2.0

# 网络请求增强
httpx>=0.23.0

# 时间处理
pytz>=2022.1

# 文本处理
jieba>=0.42.0
wordcloud>=1.8.0

# 数据验证
marshmallow>=3.17.0

# 进度条
tqdm>=4.64.0

# 配置文件处理
PyYAML>=6.0

# 数学计算
scipy>=1.9.0
scikit-learn>=1.1.0

# 金融数据处理
yfinance>=0.1.70
akshare>=1.8.0

# Web框架（如果需要API服务）
fastapi>=0.85.0
uvicorn>=0.18.0

# 数据库连接（如果需要）
sqlalchemy>=1.4.0
pymongo>=4.2.0

# 缓存
redis>=4.3.0

# 测试
pytest>=7.1.0
pytest-asyncio>=0.19.0
