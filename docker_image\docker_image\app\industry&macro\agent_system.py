"""
Multi-Agent System for Financial Research Report Generation
Implements Agent2Agent (A2A) communication and coordination
"""
import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import uuid

from data_collector import DataCollector
from llm_client import FinancialAnalysisAgent, ReportGenerator
from chart_generator import ChartGenerator
from config import AGENT_CONFIG, REPORT_TYPES

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgentRole(Enum):
    """Agent roles in the system"""
    COORDINATOR = "coordinator"
    DATA_ANALYST = "data_analyst"
    FUNDAMENTAL_ANALYST = "fundamental_analyst"
    TECHNICAL_ANALYST = "technical_analyst"
    MACRO_ANALYST = "macro_analyst"
    REPORT_WRITER = "report_writer"
    QUALITY_CHECKER = "quality_checker"
    CHART_SPECIALIST = "chart_specialist"

class TaskStatus(Enum):
    """Task execution status"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    DELEGATED = "delegated"

@dataclass
class AgentMessage:
    """Message structure for agent communication"""
    sender_id: str
    receiver_id: str
    message_type: str
    content: Dict[str, Any]
    timestamp: datetime
    task_id: str
    priority: int = 1

@dataclass
class Task:
    """Task structure for agent system"""
    task_id: str
    task_type: str
    description: str
    assigned_agent: str
    status: TaskStatus
    input_data: Dict[str, Any]
    output_data: Dict[str, Any]
    dependencies: List[str]
    created_at: datetime
    deadline: Optional[datetime] = None
    retry_count: int = 0

class Agent:
    """Base agent class"""
    
    def __init__(self, agent_id: str, role: AgentRole):
        self.agent_id = agent_id
        self.role = role
        self.message_queue = asyncio.Queue()
        self.task_queue = asyncio.Queue()
        self.memory = {}
        self.is_active = True
        logger.info(f"Agent {agent_id} ({role.value}) initialized")
    
    async def send_message(self, receiver_id: str, message_type: str, 
                          content: Dict[str, Any], task_id: str, priority: int = 1):
        """Send message to another agent"""
        message = AgentMessage(
            sender_id=self.agent_id,
            receiver_id=receiver_id,
            message_type=message_type,
            content=content,
            timestamp=datetime.now(),
            task_id=task_id,
            priority=priority
        )
        
        # In a real system, this would go through a message broker
        logger.info(f"Agent {self.agent_id} sending {message_type} to {receiver_id}")
        return message
    
    async def process_message(self, message: AgentMessage):
        """Process incoming message"""
        logger.info(f"Agent {self.agent_id} processing {message.message_type} from {message.sender_id}")
        
        if message.message_type == "task_assignment":
            await self.handle_task_assignment(message)
        elif message.message_type == "data_request":
            await self.handle_data_request(message)
        elif message.message_type == "analysis_result":
            await self.handle_analysis_result(message)
        elif message.message_type == "feedback":
            await self.handle_feedback(message)
    
    async def handle_task_assignment(self, message: AgentMessage):
        """Handle task assignment"""
        task_data = message.content
        logger.info(f"Agent {self.agent_id} received task: {task_data.get('description', 'No description')}")
    
    async def handle_data_request(self, message: AgentMessage):
        """Handle data request"""
        pass
    
    async def handle_analysis_result(self, message: AgentMessage):
        """Handle analysis result"""
        pass
    
    async def handle_feedback(self, message: AgentMessage):
        """Handle feedback"""
        pass

class DataAnalystAgent(Agent):
    """Agent specialized in data collection and preprocessing"""
    
    def __init__(self, agent_id: str):
        super().__init__(agent_id, AgentRole.DATA_ANALYST)
        self.data_collector = DataCollector()
    
    async def collect_stock_data(self, ts_code: str, period_days: int = 365) -> Dict[str, Any]:
        """Collect comprehensive stock data"""
        try:
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=period_days)).strftime('%Y%m%d')
            
            # Collect various data types
            basic_info = self.data_collector.get_stock_basic_info(ts_code)
            daily_data = self.data_collector.get_stock_daily_data(ts_code, start_date, end_date)
            financial_statements = self.data_collector.get_financial_statements(ts_code)
            financial_indicators = self.data_collector.get_financial_indicators(ts_code)
            
            # Add technical indicators
            if not daily_data.empty:
                daily_data = self.data_collector.calculate_technical_indicators(daily_data)
            
            data_package = {
                "basic_info": basic_info,
                "daily_data": daily_data,
                "financial_statements": financial_statements,
                "financial_indicators": financial_indicators,
                "collection_timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"Data collection completed for {ts_code}")
            return data_package
            
        except Exception as e:
            logger.error(f"Error collecting stock data for {ts_code}: {e}")
            return {"error": str(e)}
    
    async def collect_macro_data(self, indicators: List[str]) -> Dict[str, Any]:
        """Collect macroeconomic data"""
        try:
            macro_data = {}
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=1825)).strftime('%Y%m%d')  # 5 years
            
            for indicator in indicators:
                data = self.data_collector.get_macro_data(indicator, start_date, end_date)
                if not data.empty:
                    macro_data[indicator] = data
            
            logger.info(f"Macro data collection completed for {len(indicators)} indicators")
            return macro_data
            
        except Exception as e:
            logger.error(f"Error collecting macro data: {e}")
            return {"error": str(e)}

class FundamentalAnalystAgent(Agent):
    """Agent specialized in fundamental analysis"""
    
    def __init__(self, agent_id: str):
        super().__init__(agent_id, AgentRole.FUNDAMENTAL_ANALYST)
        self.analysis_agent = FinancialAnalysisAgent()
    
    async def analyze_financial_statements(self, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform fundamental analysis on financial statements"""
        try:
            statements = financial_data.get("financial_statements", {})
            indicators = financial_data.get("financial_indicators", {})
            
            # Prepare analysis context
            context = f"""
            财务报表数据:
            {json.dumps(statements, ensure_ascii=False, indent=2)}
            
            财务指标:
            {json.dumps(indicators, ensure_ascii=False, indent=2) if hasattr(indicators, 'to_dict') else str(indicators)}
            """
            
            # Generate fundamental analysis
            analysis = self.analysis_agent.analyze_financial_data(
                data_context=context,
                analysis_type="基本面分析",
                specific_requirements="重点分析盈利能力、偿债能力、营运能力和成长能力"
            )
            
            return {
                "fundamental_analysis": analysis,
                "analysis_timestamp": datetime.now().isoformat(),
                "analyst_id": self.agent_id
            }
            
        except Exception as e:
            logger.error(f"Error in fundamental analysis: {e}")
            return {"error": str(e)}

class TechnicalAnalystAgent(Agent):
    """Agent specialized in technical analysis"""
    
    def __init__(self, agent_id: str):
        super().__init__(agent_id, AgentRole.TECHNICAL_ANALYST)
        self.analysis_agent = FinancialAnalysisAgent()
    
    async def analyze_technical_indicators(self, price_data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform technical analysis on price data"""
        try:
            daily_data = price_data.get("daily_data")
            if daily_data is None or daily_data.empty:
                return {"error": "No price data available"}
            
            # Prepare technical analysis context
            latest_data = daily_data.tail(20)  # Last 20 days
            context = f"""
            技术指标数据 (最近20个交易日):
            {latest_data.to_string()}
            
            当前价格: {daily_data['close'].iloc[-1]:.2f}
            MA20: {daily_data['MA20'].iloc[-1]:.2f if 'MA20' in daily_data.columns else 'N/A'}
            RSI: {daily_data['RSI'].iloc[-1]:.2f if 'RSI' in daily_data.columns else 'N/A'}
            """
            
            # Generate technical analysis
            analysis = self.analysis_agent.analyze_financial_data(
                data_context=context,
                analysis_type="技术分析",
                specific_requirements="分析价格趋势、支撑阻力位、技术指标信号和交易建议"
            )
            
            return {
                "technical_analysis": analysis,
                "analysis_timestamp": datetime.now().isoformat(),
                "analyst_id": self.agent_id
            }
            
        except Exception as e:
            logger.error(f"Error in technical analysis: {e}")
            return {"error": str(e)}

class ChartSpecialistAgent(Agent):
    """Agent specialized in chart generation"""
    
    def __init__(self, agent_id: str):
        super().__init__(agent_id, AgentRole.CHART_SPECIALIST)
        self.chart_generator = ChartGenerator()
    
    async def create_comprehensive_charts(self, data_package: Dict[str, Any], 
                                        analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """Create comprehensive charts for the report"""
        try:
            chart_paths = []
            chart_descriptions = []
            
            # Stock price chart
            daily_data = data_package.get("daily_data")
            if daily_data is not None and not daily_data.empty:
                price_chart = self.chart_generator.create_stock_price_chart(
                    daily_data, 
                    title="股价走势与技术指标"
                )
                if price_chart:
                    chart_paths.append(price_chart)
                    chart_descriptions.append("股价走势图：展示收盘价、移动平均线和成交量")
            
            # Financial ratios chart
            indicators = data_package.get("financial_indicators")
            if hasattr(indicators, 'iloc') and not indicators.empty:
                latest_indicators = indicators.iloc[0]
                ratios_data = {
                    "ROE": latest_indicators.get('roe', 0),
                    "ROA": latest_indicators.get('roa', 0),
                    "净利润率": latest_indicators.get('netprofit_margin', 0),
                    "毛利率": latest_indicators.get('grossprofit_margin', 0)
                }
                
                ratios_chart = self.chart_generator.create_financial_ratios_chart(
                    ratios_data,
                    title="关键财务比率"
                )
                if ratios_chart:
                    chart_paths.append(ratios_chart)
                    chart_descriptions.append("财务比率图：展示ROE、ROA、净利润率等关键指标")
            
            # Interactive chart
            if daily_data is not None and not daily_data.empty:
                interactive_chart = self.chart_generator.create_plotly_interactive_chart(
                    daily_data,
                    chart_type="candlestick",
                    title="交互式K线图"
                )
                if interactive_chart:
                    chart_paths.append(interactive_chart)
                    chart_descriptions.append("交互式K线图：支持缩放和详细数据查看")
            
            return {
                "chart_paths": chart_paths,
                "chart_descriptions": chart_descriptions,
                "charts_count": len(chart_paths),
                "generation_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error creating charts: {e}")
            return {"error": str(e)}

class ReportWriterAgent(Agent):
    """Agent specialized in report writing"""
    
    def __init__(self, agent_id: str):
        super().__init__(agent_id, AgentRole.REPORT_WRITER)
        self.report_generator = ReportGenerator()
    
    async def generate_report(self, report_type: str, data_package: Dict[str, Any],
                            analysis_results: Dict[str, Any], 
                            chart_info: Dict[str, Any]) -> Dict[str, Any]:
        """Generate complete financial research report"""
        try:
            # Consolidate all data for report generation
            consolidated_data = {
                **data_package,
                "analysis_results": analysis_results,
                "chart_info": chart_info
            }
            
            # Generate report sections
            report_sections = self.report_generator.generate_complete_report(
                report_type=report_type,
                data_package=consolidated_data,
                custom_requirements=""
            )
            
            # Add metadata
            report_metadata = {
                "report_type": report_type,
                "generation_timestamp": datetime.now().isoformat(),
                "writer_agent_id": self.agent_id,
                "sections_count": len(report_sections),
                "chart_count": chart_info.get("charts_count", 0)
            }
            
            return {
                "report_sections": report_sections,
                "metadata": report_metadata,
                "status": "completed"
            }
            
        except Exception as e:
            logger.error(f"Error generating report: {e}")
            return {"error": str(e), "status": "failed"}

class CoordinatorAgent(Agent):
    """Main coordinator agent that orchestrates the entire process"""
    
    def __init__(self, agent_id: str):
        super().__init__(agent_id, AgentRole.COORDINATOR)
        self.agents = {}
        self.tasks = {}
        self.workflow_status = {}
    
    def register_agent(self, agent: Agent):
        """Register an agent in the system"""
        self.agents[agent.agent_id] = agent
        logger.info(f"Agent {agent.agent_id} registered with coordinator")
    
    async def orchestrate_report_generation(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Orchestrate the complete report generation workflow"""
        try:
            workflow_id = str(uuid.uuid4())
            report_type = request.get("report_type", "company")
            target_symbol = request.get("symbol", "000001.SZ")
            
            logger.info(f"Starting workflow {workflow_id} for {report_type} report on {target_symbol}")
            
            # Step 1: Data Collection
            data_analyst = self.agents.get("data_analyst")
            if not data_analyst:
                return {"error": "Data analyst agent not available"}
            
            if report_type in ["company", "individual_stock"]:
                data_package = await data_analyst.collect_stock_data(target_symbol)
            else:
                # For macro and industry reports
                indicators = ["GDP", "CPI", "PPI", "利率"]
                data_package = await data_analyst.collect_macro_data(indicators)
            
            if "error" in data_package:
                return {"error": f"Data collection failed: {data_package['error']}"}
            
            # Step 2: Analysis
            analysis_results = {}
            
            # Fundamental analysis (for company reports)
            if report_type in ["company", "individual_stock"]:
                fundamental_analyst = self.agents.get("fundamental_analyst")
                if fundamental_analyst:
                    fundamental_result = await fundamental_analyst.analyze_financial_statements(data_package)
                    analysis_results["fundamental"] = fundamental_result
                
                # Technical analysis
                technical_analyst = self.agents.get("technical_analyst")
                if technical_analyst:
                    technical_result = await technical_analyst.analyze_technical_indicators(data_package)
                    analysis_results["technical"] = technical_result
            
            # Step 3: Chart Generation
            chart_specialist = self.agents.get("chart_specialist")
            chart_info = {}
            if chart_specialist:
                chart_info = await chart_specialist.create_comprehensive_charts(data_package, analysis_results)
            
            # Step 4: Report Writing
            report_writer = self.agents.get("report_writer")
            if not report_writer:
                return {"error": "Report writer agent not available"}
            
            final_report = await report_writer.generate_report(
                report_type, data_package, analysis_results, chart_info
            )
            
            # Compile final result
            result = {
                "workflow_id": workflow_id,
                "report_type": report_type,
                "target_symbol": target_symbol,
                "data_package": data_package,
                "analysis_results": analysis_results,
                "chart_info": chart_info,
                "final_report": final_report,
                "completion_timestamp": datetime.now().isoformat(),
                "status": "completed"
            }
            
            logger.info(f"Workflow {workflow_id} completed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Error in workflow orchestration: {e}")
            return {"error": str(e), "status": "failed"}

class AgentSystem:
    """Main agent system class"""
    
    def __init__(self):
        """Initialize the multi-agent system"""
        self.coordinator = CoordinatorAgent("coordinator_001")
        
        # Initialize specialized agents
        self.data_analyst = DataAnalystAgent("data_analyst_001")
        self.fundamental_analyst = FundamentalAnalystAgent("fundamental_analyst_001")
        self.technical_analyst = TechnicalAnalystAgent("technical_analyst_001")
        self.chart_specialist = ChartSpecialistAgent("chart_specialist_001")
        self.report_writer = ReportWriterAgent("report_writer_001")
        
        # Register agents with coordinator using role-based keys
        self.coordinator.agents["data_analyst"] = self.data_analyst
        self.coordinator.agents["fundamental_analyst"] = self.fundamental_analyst
        self.coordinator.agents["technical_analyst"] = self.technical_analyst
        self.coordinator.agents["chart_specialist"] = self.chart_specialist
        self.coordinator.agents["report_writer"] = self.report_writer
        
        logger.info("Agent system initialized with all specialized agents")
    
    async def generate_research_report(self, report_request: Dict[str, Any]) -> Dict[str, Any]:
        """Main entry point for report generation"""
        try:
            # Validate request
            if "report_type" not in report_request:
                return {"error": "Report type not specified"}
            
            if "symbol" not in report_request and report_request["report_type"] in ["company", "individual_stock"]:
                return {"error": "Stock symbol required for company reports"}
            
            # Execute workflow
            result = await self.coordinator.orchestrate_report_generation(report_request)
            return result
            
        except Exception as e:
            logger.error(f"Error in research report generation: {e}")
            return {"error": str(e)}

if __name__ == "__main__":
    async def test_agent_system():
        """Test the agent system"""
        system = AgentSystem()
        
        # Test company report generation
        request = {
            "report_type": "company",
            "symbol": "000001.SZ",
            "analysis_depth": "detailed"
        }
        
        print("Testing Agent System...")
        print(f"Generating report for {request['symbol']}...")
        
        result = await system.generate_research_report(request)
        
        if "error" in result:
            print(f"Error: {result['error']}")
        else:
            print(f"Report generated successfully!")
            print(f"Workflow ID: {result.get('workflow_id')}")
            print(f"Charts generated: {result.get('chart_info', {}).get('charts_count', 0)}")
            print(f"Report sections: {len(result.get('final_report', {}).get('report_sections', {}))}")
        
        return result
    
    # Run test
    asyncio.run(test_agent_system())