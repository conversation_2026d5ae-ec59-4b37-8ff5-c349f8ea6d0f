2025-07-27 14:59:34,632 - __main__ - INFO - ✅ 配置加载成功
2025-07-27 14:59:34,633 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-27 14:59:34,633 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-27 14:59:34,633 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-27 14:59:34,634 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-27 14:59:34,634 - __main__ - INFO - 📊 公司名称: 4Paradigm
2025-07-27 14:59:34,634 - __main__ - INFO - 📈 股票代码: 4Paradigm
2025-07-27 14:59:34,638 - __main__ - INFO - ⏰ 开始时间: 2025-07-27 14:59:34
2025-07-27 14:59:34,638 - __main__ - INFO - ================================================================================
2025-07-27 14:59:34,638 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 4Paradigm
2025-07-27 14:59:34,639 - __main__ - INFO - ================================================================================
2025-07-27 14:59:34,639 - __main__ - INFO - 
==================== 步骤1: 数据爬取 ====================
2025-07-27 14:59:34,639 - __main__ - INFO - 🔄 执行步骤: 数据爬取 (crawler.py)
2025-07-27 14:59:38,833 - __main__ - INFO - ❌ 失败 数据爬取 (crawler.py) - 耗时: 4.2秒 异常: 'CompanyResearchReportPipeline' object has no attribute '_decode_with_fallback'
2025-07-27 14:59:38,833 - __main__ - ERROR - 脚本执行异常: crawler.py, 错误: 'CompanyResearchReportPipeline' object has no attribute '_decode_with_fallback'
2025-07-27 14:59:38,834 - __main__ - ERROR - ❌ 步骤1 执行失败 (必需步骤)
2025-07-27 14:59:38,834 - __main__ - ERROR - 🛑 流水线执行中断
2025-07-27 14:59:38,835 - __main__ - INFO - 📋 执行报告已生成: execution_report_4Paradigm_20250727_145938.md
2025-07-27 14:59:38,835 - __main__ - ERROR - 💥 研报生成流水线执行失败
2025-07-27 15:00:52,177 - __main__ - INFO - ✅ 配置加载成功
2025-07-27 15:00:52,178 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-27 15:00:52,178 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-27 15:00:52,178 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-27 15:00:52,179 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-27 15:00:52,179 - __main__ - INFO - 📊 公司名称: 4Paradigm
2025-07-27 15:00:52,179 - __main__ - INFO - 📈 股票代码: 4Paradigm
2025-07-27 15:00:52,179 - __main__ - INFO - ⏰ 开始时间: 2025-07-27 15:00:52
2025-07-27 15:00:52,179 - __main__ - INFO - ================================================================================
2025-07-27 15:00:52,179 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 4Paradigm
2025-07-27 15:00:52,180 - __main__ - INFO - ================================================================================
2025-07-27 15:00:52,180 - __main__ - INFO - 
==================== 步骤1: 数据爬取 ====================
2025-07-27 15:00:52,180 - __main__ - INFO - 🔄 执行步骤: 数据爬取 (crawler.py)
2025-07-27 15:01:02,118 - __main__ - INFO - ❌ 失败 数据爬取 (crawler.py) - 耗时: 9.9秒 错误: Traceback (most recent call last):

  File "C:\Users\<USER>\Desktop\results\docker_image\docker_image...
2025-07-27 15:01:02,119 - __main__ - ERROR - 脚本执行失败: crawler.py
2025-07-27 15:01:02,119 - __main__ - ERROR - 返回码: 1
2025-07-27 15:01:02,119 - __main__ - ERROR - 错误信息: Traceback (most recent call last):

  File "C:\Users\<USER>\Desktop\results\docker_image\docker_image\app\crawler.py", line 239, in fetch_main_indicators

    print(f"\u2713 主要财务指标 成功获取 {len(result_data)} 条记录，保留 {len(available_fields)} 个核心字段")

UnicodeEncodeError: 'gbk' codec can't encode character '\u2713' in position 0: illegal multibyte sequence



During handling of the above exception, another exception occurred:



Traceback (most recent call last):

  File "C:\Users\<USER>\Desktop\results\docker_image\docker_image\app\crawler.py", line 549, in <module>

    main_indicators_df = fetch_main_indicators(SECUCODE)

                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

  File "C:\Users\<USER>\Desktop\results\docker_image\docker_image\app\crawler.py", line 246, in fetch_main_indicators

    print(f"\u2717 主要财务指标 获取失败: {e}")

UnicodeEncodeError: 'gbk' codec can't encode character '\u2717' in position 0: illegal multibyte sequence


2025-07-27 15:01:02,119 - __main__ - INFO - 标准输出: 开始自动抓取所有财务表和行业对比分析...

============================================================

正在抓取：资产负债表 (尝试1/3)

资产负债表 已保存为 资产负债表.csv

正在抓取：利润表 (尝试1/3)

利润表 已保存为 利润表.csv

正在抓取：现金流量表 (尝试1/3)

现金流量表 已保存为 现金流量表.csv

正在获取主要财务指标...

...
2025-07-27 15:01:02,119 - __main__ - ERROR - ❌ 步骤1 执行失败 (必需步骤)
2025-07-27 15:01:02,120 - __main__ - ERROR - 🛑 流水线执行中断
2025-07-27 15:01:02,120 - __main__ - INFO - 📋 执行报告已生成: execution_report_4Paradigm_20250727_150102.md
2025-07-27 15:01:02,121 - __main__ - ERROR - 💥 研报生成流水线执行失败
2025-07-27 15:02:33,705 - __main__ - INFO - ✅ 配置加载成功
2025-07-27 15:02:33,706 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-27 15:02:33,706 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-27 15:02:33,706 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-27 15:02:33,706 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-27 15:02:33,706 - __main__ - INFO - 📊 公司名称: 4Paradigm
2025-07-27 15:02:33,706 - __main__ - INFO - 📈 股票代码: 4Paradigm
2025-07-27 15:02:33,707 - __main__ - INFO - ⏰ 开始时间: 2025-07-27 15:02:33
2025-07-27 15:02:33,707 - __main__ - INFO - ================================================================================
2025-07-27 15:02:33,707 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 4Paradigm
2025-07-27 15:02:33,707 - __main__ - INFO - ================================================================================
2025-07-27 15:02:33,707 - __main__ - INFO - 
==================== 步骤1: 数据爬取 ====================
2025-07-27 15:02:33,707 - __main__ - INFO - 🔄 执行步骤: 数据爬取 (crawler.py)
2025-07-27 15:02:46,664 - __main__ - INFO - ❌ 失败 数据爬取 (crawler.py) - 耗时: 13.0秒 错误: C:\Users\<USER>\Desktop\results\docker_image\docker_image\app\crawler.py:349: FutureWarning: The beha...
2025-07-27 15:02:46,664 - __main__ - ERROR - 脚本执行失败: crawler.py
2025-07-27 15:02:46,665 - __main__ - ERROR - 返回码: 1
2025-07-27 15:02:46,665 - __main__ - ERROR - 错误信息: C:\Users\<USER>\Desktop\results\docker_image\docker_image\app\crawler.py:349: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.

  merged_df = pd.concat(all_dfs, ignore_index=True, sort=False)

C:\Users\<USER>\Desktop\results\docker_image\docker_image\app\crawler.py:379: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.

  merged_df = pd.concat(all_dfs, ignore_index=True, sort=False)

C:\Users\<USER>\Desktop\results\docker_image\docker_image\app\crawler.py:409: FutureWarning: The behavior of DataFrame concatenation with empty or all-NA entries is deprecated. In a future version, this will no longer exclude empty or all-NA columns when determining the result dtypes. To retain the old behavior, exclude the relevant entries before the concat operation.

  merged_df = pd.concat(all_dfs, ignore_index=True, sort=False)

Traceback (most recent call last):

  File "C:\Users\<USER>\Desktop\results\docker_image\docker_image\app\crawler.py", line 562, in <module>

    print("\u2705 全部数据抓取与行业对比分析完成！")

UnicodeEncodeError: 'gbk' codec can't encode character '\u2705' in position 0: illegal multibyte sequence


2025-07-27 15:02:46,665 - __main__ - INFO - 标准输出: 开始自动抓取所有财务表和行业对比分析...

============================================================

正在抓取：资产负债表 (尝试1/3)

资产负债表 已保存为 资产负债表.csv

正在抓取：利润表 (尝试1/3)

利润表 已保存为 利润表.csv

正在抓取：现金流量表 (尝试1/3)

现金流量表 已保存为 现金流量表.csv

正在获取主要财务指标...

[成功] 主要财务指标 成功获取 16 条记录，保留 35 个核心字段

  保存为: 主要指标.csv



成长性对比数据

----------------------------------------

正在获取: 成长性-行业平均对比

正在获取: 成长性-同业公司对比

[成功] 成长性对比数据 合并完成: 成长性对比.csv (共22条记录)



估值对比数据

----------------------------------------

正在获取: 估值-行业平均对比

正在获取: 估值-同业公司对比

[成功] 估值对比数据 ...
2025-07-27 15:02:46,666 - __main__ - ERROR - ❌ 步骤1 执行失败 (必需步骤)
2025-07-27 15:02:46,666 - __main__ - ERROR - 🛑 流水线执行中断
2025-07-27 15:02:46,667 - __main__ - INFO - 📋 执行报告已生成: execution_report_4Paradigm_20250727_150246.md
2025-07-27 15:02:46,667 - __main__ - ERROR - 💥 研报生成流水线执行失败
2025-07-27 15:03:15,283 - __main__ - INFO - ✅ 配置加载成功
2025-07-27 15:03:15,284 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-27 15:03:15,284 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-27 15:03:15,284 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-27 15:03:15,284 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-27 15:03:15,285 - __main__ - INFO - 📊 公司名称: 4Paradigm
2025-07-27 15:03:15,285 - __main__ - INFO - 📈 股票代码: 4Paradigm
2025-07-27 15:03:15,285 - __main__ - INFO - ⏰ 开始时间: 2025-07-27 15:03:15
2025-07-27 15:03:15,285 - __main__ - INFO - ================================================================================
2025-07-27 15:03:15,286 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 4Paradigm
2025-07-27 15:03:15,286 - __main__ - INFO - ================================================================================
2025-07-27 15:03:15,286 - __main__ - INFO - 
==================== 步骤1: 数据爬取 ====================
2025-07-27 15:03:15,286 - __main__ - INFO - 🔄 执行步骤: 数据爬取 (crawler.py)
2025-07-27 15:03:23,316 - __main__ - INFO - ✅ 成功 数据爬取 (crawler.py) - 耗时: 8.0秒 
2025-07-27 15:03:23,316 - __main__ - INFO - ✅ 步骤1 执行成功
2025-07-27 15:03:23,316 - __main__ - INFO - 
==================== 步骤2: 观点数据爬取 ====================
2025-07-27 15:03:23,317 - __main__ - INFO - 🔄 执行步骤: 观点数据爬取 (crawler_viewpoint.py)
2025-07-27 15:13:23,346 - __main__ - INFO - ❌ 失败 观点数据爬取 (crawler_viewpoint.py) - 耗时: 600.0秒 执行超时
2025-07-27 15:13:23,346 - __main__ - ERROR - 脚本执行超时: crawler_viewpoint.py
2025-07-27 15:13:23,346 - __main__ - ERROR - ❌ 步骤2 执行失败 (必需步骤)
2025-07-27 15:13:23,346 - __main__ - ERROR - 🛑 流水线执行中断
2025-07-27 15:13:23,348 - __main__ - INFO - 📋 执行报告已生成: execution_report_4Paradigm_20250727_151323.md
2025-07-27 15:13:23,348 - __main__ - ERROR - 💥 研报生成流水线执行失败
2025-07-27 15:13:30,232 - __main__ - INFO - ✅ 配置加载成功
2025-07-27 15:13:30,232 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-27 15:13:30,232 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-27 15:13:30,233 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-27 15:13:30,233 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-27 15:13:30,233 - __main__ - INFO - 📊 公司名称: 4Paradigm
2025-07-27 15:13:30,233 - __main__ - INFO - 📈 股票代码: 4Paradigm
2025-07-27 15:13:30,233 - __main__ - INFO - ⏰ 开始时间: 2025-07-27 15:13:30
2025-07-27 15:13:30,234 - __main__ - INFO - ⏭️  跳过步骤: 2
2025-07-27 15:13:30,234 - __main__ - INFO - ================================================================================
2025-07-27 15:13:30,234 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 4Paradigm
2025-07-27 15:13:30,234 - __main__ - INFO - ================================================================================
2025-07-27 15:13:30,234 - __main__ - INFO - 
==================== 步骤1: 数据爬取 ====================
2025-07-27 15:13:30,234 - __main__ - INFO - 🔄 执行步骤: 数据爬取 (crawler.py)
2025-07-27 15:13:49,415 - __main__ - INFO - ✅ 成功 数据爬取 (crawler.py) - 耗时: 19.2秒 
2025-07-27 15:13:49,415 - __main__ - INFO - ✅ 步骤1 执行成功
2025-07-27 15:13:49,416 - __main__ - INFO - ⏭️  跳过 步骤2: 观点数据爬取
2025-07-27 15:13:49,416 - __main__ - INFO - 
==================== 步骤3: 可视化图表生成 ====================
2025-07-27 15:13:49,416 - __main__ - INFO - 🔄 执行步骤: 可视化图表生成 (visualize_report.py)
2025-07-27 15:13:52,430 - __main__ - INFO - ❌ 失败 可视化图表生成 (visualize_report.py) - 耗时: 3.0秒 错误: Traceback (most recent call last):

  File "C:\Users\<USER>\Desktop\results\docker_image\docker_image...
2025-07-27 15:13:52,431 - __main__ - ERROR - 脚本执行失败: visualize_report.py
2025-07-27 15:13:52,431 - __main__ - ERROR - 返回码: 1
2025-07-27 15:13:52,431 - __main__ - ERROR - 错误信息: Traceback (most recent call last):

  File "C:\Users\<USER>\Desktop\results\docker_image\docker_image\app\visualize_report.py", line 60, in <module>

    print(f"{exists} {font_name}: {font_path}")

UnicodeEncodeError: 'gbk' codec can't encode character '\u2705' in position 0: illegal multibyte sequence


2025-07-27 15:13:52,432 - __main__ - INFO - 标准输出: 

=== 字体文件检查 ===

...
2025-07-27 15:13:52,432 - __main__ - ERROR - ❌ 步骤3 执行失败 (必需步骤)
2025-07-27 15:13:52,432 - __main__ - ERROR - 🛑 流水线执行中断
2025-07-27 15:13:52,433 - __main__ - INFO - 📋 执行报告已生成: execution_report_4Paradigm_20250727_151352.md
2025-07-27 15:13:52,433 - __main__ - ERROR - 💥 研报生成流水线执行失败
2025-07-27 15:16:01,538 - __main__ - INFO - ✅ 配置加载成功
2025-07-27 15:16:01,538 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-27 15:16:01,539 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-27 15:16:01,539 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-27 15:16:01,539 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-27 15:16:01,539 - __main__ - INFO - 📊 公司名称: 4Paradigm
2025-07-27 15:16:01,540 - __main__ - INFO - 📈 股票代码: 4Paradigm
2025-07-27 15:16:01,540 - __main__ - INFO - ⏰ 开始时间: 2025-07-27 15:16:01
2025-07-27 15:16:01,540 - __main__ - INFO - ⏭️  跳过步骤: 2
2025-07-27 15:16:01,540 - __main__ - INFO - ================================================================================
2025-07-27 15:16:01,540 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 4Paradigm
2025-07-27 15:16:01,540 - __main__ - INFO - ================================================================================
2025-07-27 15:16:01,541 - __main__ - INFO - 
==================== 步骤1: 数据爬取 ====================
2025-07-27 15:16:01,541 - __main__ - INFO - 🔄 执行步骤: 数据爬取 (crawler.py)
2025-07-27 15:16:23,277 - __main__ - INFO - ✅ 成功 数据爬取 (crawler.py) - 耗时: 21.7秒 
2025-07-27 15:16:23,277 - __main__ - INFO - ✅ 步骤1 执行成功
2025-07-27 15:16:23,278 - __main__ - INFO - ⏭️  跳过 步骤2: 观点数据爬取
2025-07-27 15:16:23,278 - __main__ - INFO - 
==================== 步骤3: 可视化图表生成 ====================
2025-07-27 15:16:23,279 - __main__ - INFO - 🔄 执行步骤: 可视化图表生成 (visualize_report.py)
2025-07-27 15:18:25,683 - __main__ - INFO - ✅ 成功 可视化图表生成 (visualize_report.py) - 耗时: 122.4秒 
2025-07-27 15:18:25,683 - __main__ - INFO - ✅ 步骤3 执行成功
2025-07-27 15:18:25,684 - __main__ - INFO - 
==================== 步骤4: 财务分析 ====================
2025-07-27 15:18:25,684 - __main__ - INFO - 🔄 执行步骤: 财务分析 (financial_agent.py)
2025-07-27 15:18:25,774 - __main__ - INFO - ❌ 失败 财务分析 (financial_agent.py) - 耗时: 0.1秒 错误: Traceback (most recent call last):

  File "C:\Users\<USER>\Desktop\results\docker_image\docker_image...
2025-07-27 15:18:25,775 - __main__ - ERROR - 脚本执行失败: financial_agent.py
2025-07-27 15:18:25,775 - __main__ - ERROR - 返回码: 1
2025-07-27 15:18:25,775 - __main__ - ERROR - 错误信息: Traceback (most recent call last):

  File "C:\Users\<USER>\Desktop\results\docker_image\docker_image\app\financial_agent.py", line 94, in <module>

    print("\U0001f680 启动财务分析Agent...")

UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f680' in position 0: illegal multibyte sequence


2025-07-27 15:18:25,775 - __main__ - ERROR - ❌ 步骤4 执行失败 (必需步骤)
2025-07-27 15:18:25,775 - __main__ - ERROR - 🛑 流水线执行中断
2025-07-27 15:18:25,776 - __main__ - INFO - 📋 执行报告已生成: execution_report_4Paradigm_20250727_151825.md
2025-07-27 15:18:25,776 - __main__ - ERROR - 💥 研报生成流水线执行失败
2025-07-27 15:19:18,686 - __main__ - INFO - ✅ 配置加载成功
2025-07-27 15:19:18,686 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-27 15:19:18,686 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-27 15:19:18,687 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-27 15:19:18,687 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-27 15:19:18,687 - __main__ - INFO - 📊 公司名称: 4Paradigm
2025-07-27 15:19:18,687 - __main__ - INFO - 📈 股票代码: 4Paradigm
2025-07-27 15:19:18,687 - __main__ - INFO - ⏰ 开始时间: 2025-07-27 15:19:18
2025-07-27 15:19:18,688 - __main__ - INFO - ⏭️  跳过步骤: 2
2025-07-27 15:19:18,688 - __main__ - INFO - ================================================================================
2025-07-27 15:19:18,688 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 4Paradigm
2025-07-27 15:19:18,688 - __main__ - INFO - ================================================================================
2025-07-27 15:19:18,688 - __main__ - INFO - 
==================== 步骤1: 数据爬取 ====================
2025-07-27 15:19:18,688 - __main__ - INFO - 🔄 执行步骤: 数据爬取 (crawler.py)
2025-07-27 15:19:31,486 - __main__ - INFO - ✅ 成功 数据爬取 (crawler.py) - 耗时: 12.8秒 
2025-07-27 15:19:31,487 - __main__ - INFO - ✅ 步骤1 执行成功
2025-07-27 15:19:31,487 - __main__ - INFO - ⏭️  跳过 步骤2: 观点数据爬取
2025-07-27 15:19:31,487 - __main__ - INFO - 
==================== 步骤3: 可视化图表生成 ====================
2025-07-27 15:19:31,487 - __main__ - INFO - 🔄 执行步骤: 可视化图表生成 (visualize_report.py)
2025-07-27 15:21:33,581 - __main__ - INFO - ✅ 成功 可视化图表生成 (visualize_report.py) - 耗时: 122.1秒 
2025-07-27 15:21:33,581 - __main__ - INFO - ✅ 步骤3 执行成功
2025-07-27 15:21:33,582 - __main__ - INFO - 
==================== 步骤4: 财务分析 ====================
2025-07-27 15:21:33,582 - __main__ - INFO - 🔄 执行步骤: 财务分析 (financial_agent.py)
2025-07-27 15:22:56,919 - __main__ - INFO - ✅ 成功 财务分析 (financial_agent.py) - 耗时: 83.3秒 
2025-07-27 15:22:56,919 - __main__ - INFO - ✅ 步骤4 执行成功
2025-07-27 15:22:56,920 - __main__ - INFO - 
==================== 步骤5: 股吧观点分析 ====================
2025-07-27 15:22:56,920 - __main__ - INFO - 🔄 执行步骤: 股吧观点分析 (guba_viewpoint_agent.py)
2025-07-27 15:32:56,954 - __main__ - INFO - ❌ 失败 股吧观点分析 (guba_viewpoint_agent.py) - 耗时: 600.0秒 执行超时
2025-07-27 15:32:56,955 - __main__ - ERROR - 脚本执行超时: guba_viewpoint_agent.py
2025-07-27 15:32:56,955 - __main__ - WARNING - ⚠️  步骤5 执行失败 (可选步骤，继续执行)
2025-07-27 15:32:56,955 - __main__ - INFO - 
==================== 步骤6: 研究报告生成 ====================
2025-07-27 15:32:56,957 - __main__ - INFO - 🔄 执行步骤: 研究报告生成 (research_report_generator.py)
2025-07-27 15:32:59,686 - __main__ - INFO - ❌ 失败 研究报告生成 (research_report_generator.py) - 耗时: 2.7秒 错误: Traceback (most recent call last):

  File "C:\Users\<USER>\Desktop\results\docker_image\docker_image...
2025-07-27 15:32:59,686 - __main__ - ERROR - 脚本执行失败: research_report_generator.py
2025-07-27 15:32:59,686 - __main__ - ERROR - 返回码: 1
2025-07-27 15:32:59,687 - __main__ - ERROR - 错误信息: Traceback (most recent call last):

  File "C:\Users\<USER>\Desktop\results\docker_image\docker_image\app\research_report_generator.py", line 1888, in <module>

    print("\U0001f916 智能研报生成系统")

UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f916' in position 0: illegal multibyte sequence


2025-07-27 15:32:59,687 - __main__ - ERROR - ❌ 步骤6 执行失败 (必需步骤)
2025-07-27 15:32:59,687 - __main__ - ERROR - 🛑 流水线执行中断
2025-07-27 15:32:59,688 - __main__ - INFO - 📋 执行报告已生成: execution_report_4Paradigm_20250727_153259.md
2025-07-27 15:32:59,689 - __main__ - ERROR - 💥 研报生成流水线执行失败
2025-07-27 15:33:26,352 - __main__ - INFO - ✅ 配置加载成功
2025-07-27 15:33:26,352 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-27 15:33:26,353 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-27 15:33:26,353 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-27 15:33:26,353 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-27 15:33:26,353 - __main__ - INFO - 📊 公司名称: 4Paradigm
2025-07-27 15:33:26,353 - __main__ - INFO - 📈 股票代码: 4Paradigm
2025-07-27 15:33:26,354 - __main__ - INFO - ⏰ 开始时间: 2025-07-27 15:33:26
2025-07-27 15:33:26,354 - __main__ - INFO - ⏭️  跳过步骤: 2
2025-07-27 15:33:26,354 - __main__ - INFO - ================================================================================
2025-07-27 15:33:26,354 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 4Paradigm
2025-07-27 15:33:26,354 - __main__ - INFO - ================================================================================
2025-07-27 15:33:26,354 - __main__ - INFO - 
==================== 步骤1: 数据爬取 ====================
2025-07-27 15:33:26,355 - __main__ - INFO - 🔄 执行步骤: 数据爬取 (crawler.py)
2025-07-27 15:33:51,480 - __main__ - INFO - ✅ 成功 数据爬取 (crawler.py) - 耗时: 25.1秒 
2025-07-27 15:33:51,481 - __main__ - INFO - ✅ 步骤1 执行成功
2025-07-27 15:33:51,481 - __main__ - INFO - ⏭️  跳过 步骤2: 观点数据爬取
2025-07-27 15:33:51,481 - __main__ - INFO - 
==================== 步骤3: 可视化图表生成 ====================
2025-07-27 15:33:51,481 - __main__ - INFO - 🔄 执行步骤: 可视化图表生成 (visualize_report.py)
2025-07-27 15:35:54,617 - __main__ - INFO - ✅ 成功 可视化图表生成 (visualize_report.py) - 耗时: 123.1秒 
2025-07-27 15:35:54,617 - __main__ - INFO - ✅ 步骤3 执行成功
2025-07-27 15:35:54,617 - __main__ - INFO - 
==================== 步骤4: 财务分析 ====================
2025-07-27 15:35:54,618 - __main__ - INFO - 🔄 执行步骤: 财务分析 (financial_agent.py)
2025-07-27 15:37:01,233 - __main__ - INFO - ✅ 成功 财务分析 (financial_agent.py) - 耗时: 66.6秒 
2025-07-27 15:37:01,233 - __main__ - INFO - ✅ 步骤4 执行成功
2025-07-27 15:37:01,233 - __main__ - INFO - 
==================== 步骤5: 股吧观点分析 ====================
2025-07-27 15:37:01,234 - __main__ - INFO - 🔄 执行步骤: 股吧观点分析 (guba_viewpoint_agent.py)
2025-07-27 15:47:01,265 - __main__ - INFO - ❌ 失败 股吧观点分析 (guba_viewpoint_agent.py) - 耗时: 600.0秒 执行超时
2025-07-27 15:47:01,265 - __main__ - ERROR - 脚本执行超时: guba_viewpoint_agent.py
2025-07-27 15:47:01,266 - __main__ - WARNING - ⚠️  步骤5 执行失败 (可选步骤，继续执行)
2025-07-27 15:47:01,266 - __main__ - INFO - 
==================== 步骤6: 研究报告生成 ====================
2025-07-27 15:47:01,267 - __main__ - INFO - 🔄 执行步骤: 研究报告生成 (research_report_generator.py)
2025-07-27 15:47:03,813 - __main__ - INFO - ❌ 失败 研究报告生成 (research_report_generator.py) - 耗时: 2.5秒 错误: Traceback (most recent call last):

  File "C:\Users\<USER>\Desktop\results\docker_image\docker_image...
2025-07-27 15:47:03,813 - __main__ - ERROR - 脚本执行失败: research_report_generator.py
2025-07-27 15:47:03,813 - __main__ - ERROR - 返回码: 1
2025-07-27 15:47:03,813 - __main__ - ERROR - 错误信息: Traceback (most recent call last):

  File "C:\Users\<USER>\Desktop\results\docker_image\docker_image\app\research_report_generator.py", line 1893, in <module>

    validate_system_requirements()

  File "C:\Users\<USER>\Desktop\results\docker_image\docker_image\app\research_report_generator.py", line 1851, in validate_system_requirements

    print("\n\U0001f50d 系统要求验证:")

UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f50d' in position 2: illegal multibyte sequence


2025-07-27 15:47:03,814 - __main__ - INFO - 标准输出: [系统] 智能研报生成系统

基于多Agent协同的专业金融研报自动生成

集成RAG、工具调用、多模态生成等前沿技术

...
2025-07-27 15:47:03,814 - __main__ - ERROR - ❌ 步骤6 执行失败 (必需步骤)
2025-07-27 15:47:03,814 - __main__ - ERROR - 🛑 流水线执行中断
2025-07-27 15:47:03,815 - __main__ - INFO - 📋 执行报告已生成: execution_report_4Paradigm_20250727_154703.md
2025-07-27 15:47:03,816 - __main__ - ERROR - 💥 研报生成流水线执行失败
2025-07-27 15:48:28,082 - __main__ - INFO - ✅ 配置加载成功
2025-07-27 15:48:28,083 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-27 15:48:28,083 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-27 15:48:28,083 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-27 15:48:28,084 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-27 15:48:28,084 - __main__ - INFO - 📊 公司名称: 4Paradigm
2025-07-27 15:48:28,084 - __main__ - INFO - 📈 股票代码: 4Paradigm
2025-07-27 15:48:28,084 - __main__ - INFO - ⏰ 开始时间: 2025-07-27 15:48:28
2025-07-27 15:48:28,084 - __main__ - INFO - ⏭️  跳过步骤: 2
2025-07-27 15:48:28,085 - __main__ - INFO - ================================================================================
2025-07-27 15:48:28,085 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 4Paradigm
2025-07-27 15:48:28,085 - __main__ - INFO - ================================================================================
2025-07-27 15:48:28,085 - __main__ - INFO - 
==================== 步骤1: 数据爬取 ====================
2025-07-27 15:48:28,085 - __main__ - INFO - 🔄 执行步骤: 数据爬取 (crawler.py)
2025-07-27 15:48:39,363 - __main__ - INFO - ✅ 成功 数据爬取 (crawler.py) - 耗时: 11.3秒 
2025-07-27 15:48:39,363 - __main__ - INFO - ✅ 步骤1 执行成功
2025-07-27 15:48:39,364 - __main__ - INFO - ⏭️  跳过 步骤2: 观点数据爬取
2025-07-27 15:48:39,364 - __main__ - INFO - 
==================== 步骤3: 可视化图表生成 ====================
2025-07-27 15:48:39,364 - __main__ - INFO - 🔄 执行步骤: 可视化图表生成 (visualize_report.py)
2025-07-27 15:50:42,514 - __main__ - INFO - ✅ 成功 可视化图表生成 (visualize_report.py) - 耗时: 123.1秒 
2025-07-27 15:50:42,514 - __main__ - INFO - ✅ 步骤3 执行成功
2025-07-27 15:50:42,515 - __main__ - INFO - 
==================== 步骤4: 财务分析 ====================
2025-07-27 15:50:42,515 - __main__ - INFO - 🔄 执行步骤: 财务分析 (financial_agent.py)
2025-07-27 15:52:14,180 - __main__ - INFO - ✅ 成功 财务分析 (financial_agent.py) - 耗时: 91.7秒 
2025-07-27 15:52:14,180 - __main__ - INFO - ✅ 步骤4 执行成功
2025-07-27 15:52:14,180 - __main__ - INFO - 
==================== 步骤5: 股吧观点分析 ====================
2025-07-27 15:52:14,181 - __main__ - INFO - 🔄 执行步骤: 股吧观点分析 (guba_viewpoint_agent.py)
2025-07-27 16:02:14,203 - __main__ - INFO - ❌ 失败 股吧观点分析 (guba_viewpoint_agent.py) - 耗时: 600.0秒 执行超时
2025-07-27 16:02:14,203 - __main__ - ERROR - 脚本执行超时: guba_viewpoint_agent.py
2025-07-27 16:02:14,204 - __main__ - WARNING - ⚠️  步骤5 执行失败 (可选步骤，继续执行)
2025-07-27 16:02:14,204 - __main__ - INFO - 
==================== 步骤6: 研究报告生成 ====================
2025-07-27 16:02:14,204 - __main__ - INFO - 🔄 执行步骤: 研究报告生成 (research_report_generator.py)
2025-07-27 16:02:16,900 - __main__ - INFO - ❌ 失败 研究报告生成 (research_report_generator.py) - 耗时: 2.7秒 错误: Traceback (most recent call last):

  File "C:\Users\<USER>\Desktop\results\docker_image\docker_image...
2025-07-27 16:02:16,901 - __main__ - ERROR - 脚本执行失败: research_report_generator.py
2025-07-27 16:02:16,901 - __main__ - ERROR - 返回码: 1
2025-07-27 16:02:16,901 - __main__ - ERROR - 错误信息: Traceback (most recent call last):

  File "C:\Users\<USER>\Desktop\results\docker_image\docker_image\app\research_report_generator.py", line 1896, in <module>

    demo_agent_communication()

  File "C:\Users\<USER>\Desktop\results\docker_image\docker_image\app\research_report_generator.py", line 1826, in demo_agent_communication

    print("\n\U0001f504 Agent通信演示:")

UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f504' in position 2: illegal multibyte sequence


2025-07-27 16:02:16,902 - __main__ - INFO - 标准输出: [系统] 智能研报生成系统

基于多Agent协同的专业金融研报自动生成

集成RAG、工具调用、多模态生成等前沿技术



[验证] 系统要求验证:

  [清单] 财务数据文件:

    [成功] 主要指标.csv

    [成功] 利润表.csv

    [成功] 资产负债表.csv

  [清单] 观点数据文件:

    [成功] guba_viewpoint_analysis.json

  [清单] 配置文件:

    [成功] config.py

  [清单] Python包:

    [成功] pandas

    [成功] matplotlib

    [成功] docx

    [成功] requests

  [目标] 系统要求验证通过

...
2025-07-27 16:02:16,902 - __main__ - ERROR - ❌ 步骤6 执行失败 (必需步骤)
2025-07-27 16:02:16,902 - __main__ - ERROR - 🛑 流水线执行中断
2025-07-27 16:02:16,904 - __main__ - INFO - 📋 执行报告已生成: execution_report_4Paradigm_20250727_160216.md
2025-07-27 16:02:16,904 - __main__ - ERROR - 💥 研报生成流水线执行失败
2025-07-27 16:02:52,827 - __main__ - INFO - ✅ 配置加载成功
2025-07-27 16:02:52,827 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-27 16:02:52,827 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-27 16:02:52,827 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-27 16:02:52,828 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-27 16:02:52,828 - __main__ - INFO - 📊 公司名称: 4Paradigm
2025-07-27 16:02:52,828 - __main__ - INFO - 📈 股票代码: 4Paradigm
2025-07-27 16:02:52,828 - __main__ - INFO - ⏰ 开始时间: 2025-07-27 16:02:52
2025-07-27 16:02:52,828 - __main__ - INFO - ⏭️  跳过步骤: 2
2025-07-27 16:02:52,829 - __main__ - INFO - ================================================================================
2025-07-27 16:02:52,829 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 4Paradigm
2025-07-27 16:02:52,829 - __main__ - INFO - ================================================================================
2025-07-27 16:02:52,829 - __main__ - INFO - 
==================== 步骤1: 数据爬取 ====================
2025-07-27 16:02:52,829 - __main__ - INFO - 🔄 执行步骤: 数据爬取 (crawler.py)
2025-07-27 16:03:03,813 - __main__ - INFO - ✅ 成功 数据爬取 (crawler.py) - 耗时: 11.0秒 
2025-07-27 16:03:03,813 - __main__ - INFO - ✅ 步骤1 执行成功
2025-07-27 16:03:03,814 - __main__ - INFO - ⏭️  跳过 步骤2: 观点数据爬取
2025-07-27 16:03:03,814 - __main__ - INFO - 
==================== 步骤3: 可视化图表生成 ====================
2025-07-27 16:03:03,814 - __main__ - INFO - 🔄 执行步骤: 可视化图表生成 (visualize_report.py)
2025-07-27 16:05:06,773 - __main__ - INFO - ✅ 成功 可视化图表生成 (visualize_report.py) - 耗时: 123.0秒 
2025-07-27 16:05:06,774 - __main__ - INFO - ✅ 步骤3 执行成功
2025-07-27 16:05:06,774 - __main__ - INFO - 
==================== 步骤4: 财务分析 ====================
2025-07-27 16:05:06,774 - __main__ - INFO - 🔄 执行步骤: 财务分析 (financial_agent.py)
2025-07-27 16:06:28,387 - __main__ - INFO - ✅ 成功 财务分析 (financial_agent.py) - 耗时: 81.6秒 
2025-07-27 16:06:28,387 - __main__ - INFO - ✅ 步骤4 执行成功
2025-07-27 16:06:28,388 - __main__ - INFO - 
==================== 步骤5: 股吧观点分析 ====================
2025-07-27 16:06:28,388 - __main__ - INFO - 🔄 执行步骤: 股吧观点分析 (guba_viewpoint_agent.py)
2025-07-27 16:16:28,427 - __main__ - INFO - ❌ 失败 股吧观点分析 (guba_viewpoint_agent.py) - 耗时: 600.0秒 执行超时
2025-07-27 16:16:28,428 - __main__ - ERROR - 脚本执行超时: guba_viewpoint_agent.py
2025-07-27 16:16:28,429 - __main__ - WARNING - ⚠️  步骤5 执行失败 (可选步骤，继续执行)
2025-07-27 16:16:28,429 - __main__ - INFO - 
==================== 步骤6: 研究报告生成 ====================
2025-07-27 16:16:28,429 - __main__ - INFO - 🔄 执行步骤: 研究报告生成 (research_report_generator.py)
2025-07-27 16:26:13,351 - __main__ - INFO - ✅ 成功 研究报告生成 (research_report_generator.py) - 耗时: 584.9秒 
2025-07-27 16:26:13,352 - __main__ - INFO - ✅ 步骤6 执行成功
2025-07-27 16:26:13,353 - __main__ - INFO - 📋 执行报告已生成: execution_report_4Paradigm_20250727_162613.md
2025-07-27 16:26:13,353 - __main__ - ERROR - 💥 研报生成流水线执行失败
2025-07-27 18:53:24,288 - __main__ - INFO - ✅ 配置加载成功
2025-07-27 18:53:24,288 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-27 18:53:24,289 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-27 18:53:24,289 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-27 18:53:24,289 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-27 18:53:24,289 - __main__ - INFO - 📊 公司名称: 4Paradigm
2025-07-27 18:53:24,290 - __main__ - INFO - 📈 股票代码: 4Paradigm
2025-07-27 18:53:24,290 - __main__ - INFO - ⏰ 开始时间: 2025-07-27 18:53:24
2025-07-27 18:53:24,290 - __main__ - INFO - ⏭️  跳过步骤: 2, 5
2025-07-27 18:53:24,290 - __main__ - INFO - ================================================================================
2025-07-27 18:53:24,290 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 4Paradigm
2025-07-27 18:53:24,291 - __main__ - INFO - ================================================================================
2025-07-27 18:53:24,291 - __main__ - INFO - 
==================== 步骤1: 数据爬取 ====================
2025-07-27 18:53:24,291 - __main__ - INFO - 🔄 执行步骤: 数据爬取 (crawler.py)
2025-07-27 18:53:45,646 - __main__ - INFO - ✅ 成功 数据爬取 (crawler.py) - 耗时: 21.4秒 
2025-07-27 18:53:45,646 - __main__ - INFO - ✅ 步骤1 执行成功
2025-07-27 18:53:45,647 - __main__ - INFO - ⏭️  跳过 步骤2: 观点数据爬取
2025-07-27 18:53:45,647 - __main__ - INFO - 
==================== 步骤3: 可视化图表生成 ====================
2025-07-27 18:53:45,647 - __main__ - INFO - 🔄 执行步骤: 可视化图表生成 (visualize_report.py)
2025-07-27 18:55:50,616 - __main__ - INFO - ✅ 成功 可视化图表生成 (visualize_report.py) - 耗时: 125.0秒 
2025-07-27 18:55:50,616 - __main__ - INFO - ✅ 步骤3 执行成功
2025-07-27 18:55:50,616 - __main__ - INFO - 
==================== 步骤4: 财务分析 ====================
2025-07-27 18:55:50,618 - __main__ - INFO - 🔄 执行步骤: 财务分析 (financial_agent.py)
2025-07-27 18:56:43,881 - __main__ - INFO - ✅ 成功 财务分析 (financial_agent.py) - 耗时: 53.3秒 
2025-07-27 18:56:43,881 - __main__ - INFO - ✅ 步骤4 执行成功
2025-07-27 18:56:43,881 - __main__ - INFO - ⏭️  跳过 步骤5: 股吧观点分析
2025-07-27 18:56:43,882 - __main__ - INFO - 
==================== 步骤6: 研究报告生成 ====================
2025-07-27 18:56:43,882 - __main__ - INFO - 🔄 执行步骤: 研究报告生成 (research_report_generator.py)
2025-07-27 19:06:10,139 - __main__ - INFO - ✅ 成功 研究报告生成 (research_report_generator.py) - 耗时: 566.3秒 
2025-07-27 19:06:10,139 - __main__ - INFO - ✅ 步骤6 执行成功
2025-07-27 19:06:10,141 - __main__ - INFO - 📋 执行报告已生成: execution_report_4Paradigm_20250727_190610.md
2025-07-27 19:06:10,142 - __main__ - ERROR - 💥 研报生成流水线执行失败
2025-07-27 20:21:19,849 - __main__ - INFO - 📋 使用简化格式: 商汤科技
2025-07-27 20:21:19,863 - __main__ - INFO - ✅ 配置加载成功
2025-07-27 20:21:19,863 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-27 20:21:19,864 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-27 20:21:19,864 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-27 20:21:19,864 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-27 20:21:19,864 - __main__ - INFO - 📊 公司名称: 商汤科技
2025-07-27 20:21:19,865 - __main__ - INFO - 📈 股票代码: 00020.HK
2025-07-27 20:21:19,865 - __main__ - INFO - ⏰ 开始时间: 2025-07-27 20:21:19
2025-07-27 20:21:19,865 - __main__ - INFO - ⏭️  跳过步骤: 2, 5
2025-07-27 20:21:19,865 - __main__ - INFO - ================================================================================
2025-07-27 20:21:19,865 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 商汤科技
2025-07-27 20:21:19,865 - __main__ - INFO - ================================================================================
2025-07-27 20:21:19,865 - __main__ - INFO - 
==================== 步骤1: 数据爬取 ====================
2025-07-27 20:21:19,866 - __main__ - INFO - 🔄 执行步骤: 数据爬取 (crawler.py)
2025-07-27 20:21:33,593 - __main__ - INFO - ✅ 成功 数据爬取 (crawler.py) - 耗时: 13.7秒 
2025-07-27 20:21:33,593 - __main__ - INFO - ✅ 步骤1 执行成功
2025-07-27 20:21:33,593 - __main__ - INFO - ⏭️  跳过 步骤2: 观点数据爬取
2025-07-27 20:21:33,594 - __main__ - INFO - 
==================== 步骤3: 可视化图表生成 ====================
2025-07-27 20:21:33,594 - __main__ - INFO - 🔄 执行步骤: 可视化图表生成 (visualize_report.py)
2025-07-27 20:23:36,150 - __main__ - INFO - ✅ 成功 可视化图表生成 (visualize_report.py) - 耗时: 122.6秒 
2025-07-27 20:23:36,150 - __main__ - INFO - ✅ 步骤3 执行成功
2025-07-27 20:23:36,151 - __main__ - INFO - 
==================== 步骤4: 财务分析 ====================
2025-07-27 20:23:36,151 - __main__ - INFO - 🔄 执行步骤: 财务分析 (financial_agent.py)
2025-07-27 20:26:19,254 - __main__ - INFO - ✅ 成功 财务分析 (financial_agent.py) - 耗时: 163.1秒 
2025-07-27 20:26:19,255 - __main__ - INFO - ✅ 步骤4 执行成功
2025-07-27 20:26:19,255 - __main__ - INFO - ⏭️  跳过 步骤5: 股吧观点分析
2025-07-27 20:26:19,256 - __main__ - INFO - 
==================== 步骤6: 研究报告生成 ====================
2025-07-27 20:26:19,256 - __main__ - INFO - 🔄 执行步骤: 研究报告生成 (research_report_generator.py)
2025-07-27 20:37:07,972 - __main__ - INFO - ✅ 成功 研究报告生成 (research_report_generator.py) - 耗时: 648.7秒 
2025-07-27 20:37:07,973 - __main__ - INFO - ✅ 步骤6 执行成功
2025-07-27 20:37:07,977 - __main__ - INFO - 📋 执行报告已生成: execution_report_00020_HK_20250727_203707.md
2025-07-27 20:37:07,978 - __main__ - ERROR - 💥 研报生成流水线执行失败
2025-07-27 20:38:54,180 - __main__ - INFO - 📋 使用完整格式: 公司名称=商汤科技, 股票代码=00020.HK
2025-07-27 20:38:54,195 - __main__ - INFO - ✅ 配置加载成功
2025-07-27 20:38:54,195 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-27 20:38:54,195 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-27 20:38:54,195 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-27 20:38:54,196 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-27 20:38:54,196 - __main__ - INFO - 📊 公司名称: 商汤科技
2025-07-27 20:38:54,196 - __main__ - INFO - 📈 股票代码: 00020.HK
2025-07-27 20:38:54,196 - __main__ - INFO - ⏰ 开始时间: 2025-07-27 20:38:54
2025-07-27 20:38:54,196 - __main__ - INFO - ⏭️  跳过步骤: 1, 2, 3, 4, 5
2025-07-27 20:38:54,196 - __main__ - INFO - ================================================================================
2025-07-27 20:38:54,197 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 商汤科技
2025-07-27 20:38:54,197 - __main__ - INFO - ================================================================================
2025-07-27 20:38:54,197 - __main__ - INFO - ⏭️  跳过 步骤1: 数据爬取
2025-07-27 20:38:54,197 - __main__ - INFO - ⏭️  跳过 步骤2: 观点数据爬取
2025-07-27 20:38:54,197 - __main__ - INFO - ⏭️  跳过 步骤3: 可视化图表生成
2025-07-27 20:38:54,197 - __main__ - INFO - ⏭️  跳过 步骤4: 财务分析
2025-07-27 20:38:54,198 - __main__ - INFO - ⏭️  跳过 步骤5: 股吧观点分析
2025-07-27 20:38:54,198 - __main__ - INFO - 
==================== 步骤6: 研究报告生成 ====================
2025-07-27 20:38:54,198 - __main__ - INFO - 🔄 执行步骤: 研究报告生成 (research_report_generator.py)
2025-07-27 20:48:28,944 - __main__ - INFO - ✅ 成功 研究报告生成 (research_report_generator.py) - 耗时: 574.7秒 
2025-07-27 20:48:28,945 - __main__ - INFO - ✅ 步骤6 执行成功
2025-07-27 20:48:28,946 - __main__ - INFO - 📋 执行报告已生成: execution_report_00020_HK_20250727_204828.md
2025-07-27 20:48:28,946 - __main__ - ERROR - 💥 研报生成流水线执行失败
2025-07-27 20:57:52,197 - __main__ - INFO - 📋 使用完整格式: 公司名称=商汤科技, 股票代码=00020.HK
2025-07-27 20:57:52,212 - __main__ - INFO - ✅ 配置加载成功
2025-07-27 20:57:52,212 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-27 20:57:52,214 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-27 20:57:52,214 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-27 20:57:52,214 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-27 20:57:52,214 - __main__ - INFO - 📊 公司名称: 商汤科技
2025-07-27 20:57:52,215 - __main__ - INFO - 📈 股票代码: 00020.HK
2025-07-27 20:57:52,215 - __main__ - INFO - ⏰ 开始时间: 2025-07-27 20:57:52
2025-07-27 20:57:52,215 - __main__ - INFO - ================================================================================
2025-07-27 20:57:52,215 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 商汤科技
2025-07-27 20:57:52,215 - __main__ - INFO - ================================================================================
2025-07-27 20:57:52,216 - __main__ - INFO - 
==================== 步骤1: 数据爬取 ====================
2025-07-27 20:57:52,216 - __main__ - INFO - 🔄 执行步骤: 数据爬取 (crawler.py)
2025-07-27 20:58:21,493 - __main__ - INFO - ✅ 成功 数据爬取 (crawler.py) - 耗时: 29.3秒 
2025-07-27 20:58:21,494 - __main__ - INFO - ✅ 步骤1 执行成功
2025-07-27 20:58:21,494 - __main__ - INFO - 
==================== 步骤2: 观点数据爬取 ====================
2025-07-27 20:58:21,494 - __main__ - INFO - 🔄 执行步骤: 观点数据爬取 (crawler_viewpoint.py)
2025-07-27 21:08:21,520 - __main__ - INFO - ❌ 失败 观点数据爬取 (crawler_viewpoint.py) - 耗时: 600.0秒 执行超时
2025-07-27 21:08:21,520 - __main__ - ERROR - 脚本执行超时: crawler_viewpoint.py
2025-07-27 21:08:21,520 - __main__ - ERROR - ❌ 步骤2 执行失败 (必需步骤)
2025-07-27 21:08:21,521 - __main__ - ERROR - 🛑 流水线执行中断
2025-07-27 21:08:21,522 - __main__ - INFO - 📋 执行报告已生成: execution_report_00020_HK_20250727_210821.md
2025-07-27 21:08:21,522 - __main__ - ERROR - 💥 研报生成流水线执行失败
2025-07-27 21:10:45,718 - __main__ - INFO - 📋 使用完整格式: 公司名称=商汤科技, 股票代码=00020.HK
2025-07-27 21:10:45,733 - __main__ - INFO - ✅ 配置加载成功
2025-07-27 21:10:45,733 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-27 21:10:45,733 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-27 21:10:45,734 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-27 21:10:45,734 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-27 21:10:45,734 - __main__ - INFO - 📊 公司名称: 商汤科技
2025-07-27 21:10:45,734 - __main__ - INFO - 📈 股票代码: 00020.HK
2025-07-27 21:10:45,734 - __main__ - INFO - ⏰ 开始时间: 2025-07-27 21:10:45
2025-07-27 21:10:45,735 - __main__ - INFO - ================================================================================
2025-07-27 21:10:45,735 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 商汤科技
2025-07-27 21:10:45,735 - __main__ - INFO - ================================================================================
2025-07-27 21:10:45,735 - __main__ - INFO - 
==================== 步骤1: 数据爬取 ====================
2025-07-27 21:10:45,735 - __main__ - INFO - 🔄 执行步骤: 数据爬取 (crawler.py)
2025-07-27 21:10:53,271 - __main__ - INFO - ✅ 成功 数据爬取 (crawler.py) - 耗时: 7.5秒 
2025-07-27 21:10:53,271 - __main__ - INFO - ✅ 步骤1 执行成功
2025-07-27 21:10:53,272 - __main__ - INFO - 
==================== 步骤2: 观点数据爬取 ====================
2025-07-27 21:10:53,272 - __main__ - INFO - 🔄 执行步骤: 观点数据爬取 (crawler_viewpoint.py)
2025-07-27 21:20:53,293 - __main__ - INFO - ❌ 失败 观点数据爬取 (crawler_viewpoint.py) - 耗时: 600.0秒 执行超时
2025-07-27 21:20:53,294 - __main__ - ERROR - 脚本执行超时: crawler_viewpoint.py
2025-07-27 21:20:53,294 - __main__ - ERROR - ❌ 步骤2 执行失败 (必需步骤)
2025-07-27 21:20:53,295 - __main__ - ERROR - 🛑 流水线执行中断
2025-07-27 21:20:53,296 - __main__ - INFO - 📋 执行报告已生成: execution_report_00020_HK_20250727_212053.md
2025-07-27 21:20:53,296 - __main__ - ERROR - 💥 研报生成流水线执行失败
2025-07-27 21:29:38,335 - __main__ - INFO - 📋 使用简化格式: 商汤科技
2025-07-27 21:29:38,339 - __main__ - INFO - ✅ 配置加载成功
2025-07-27 21:29:38,339 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-27 21:29:38,339 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-27 21:29:38,340 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-27 21:29:38,340 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-27 21:29:38,340 - __main__ - INFO - 📊 公司名称: 商汤科技
2025-07-27 21:29:38,341 - __main__ - INFO - 📈 股票代码: 00020.HK
2025-07-27 21:29:38,341 - __main__ - INFO - ⏰ 开始时间: 2025-07-27 21:29:38
2025-07-27 21:29:38,341 - __main__ - INFO - ⏭️  跳过步骤: 1, 3, 4, 5, 6
2025-07-27 21:29:38,341 - __main__ - INFO - ================================================================================
2025-07-27 21:29:38,341 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 商汤科技
2025-07-27 21:29:38,342 - __main__ - INFO - ================================================================================
2025-07-27 21:29:38,342 - __main__ - INFO - ⏭️  跳过 步骤1: 数据爬取
2025-07-27 21:29:38,342 - __main__ - INFO - 
==================== 步骤2: 观点数据爬取 ====================
2025-07-27 21:29:38,342 - __main__ - INFO - 🔄 执行步骤: 观点数据爬取 (crawler_viewpoint.py)
2025-07-27 21:30:38,372 - __main__ - INFO - ❌ 失败 观点数据爬取 (crawler_viewpoint.py) - 耗时: 60.0秒 执行超时
2025-07-27 21:30:38,373 - __main__ - ERROR - 脚本执行超时: crawler_viewpoint.py
2025-07-27 21:30:38,373 - __main__ - ERROR - ❌ 步骤2 执行失败 (必需步骤)
2025-07-27 21:30:38,374 - __main__ - ERROR - 🛑 流水线执行中断
2025-07-27 21:30:38,375 - __main__ - INFO - 📋 执行报告已生成: execution_report_00020_HK_20250727_213038.md
2025-07-27 21:30:38,375 - __main__ - ERROR - 💥 研报生成流水线执行失败
2025-07-27 21:36:36,869 - __main__ - INFO - 📋 使用完整格式: 公司名称=商汤科技, 股票代码=00020.HK
2025-07-27 21:36:36,873 - __main__ - INFO - ✅ 配置加载成功
2025-07-27 21:36:36,873 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-27 21:36:36,873 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-27 21:36:36,874 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-27 21:36:36,874 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-27 21:36:36,874 - __main__ - INFO - 📊 公司名称: 商汤科技
2025-07-27 21:36:36,874 - __main__ - INFO - 📈 股票代码: 00020.HK
2025-07-27 21:36:36,874 - __main__ - INFO - ⏰ 开始时间: 2025-07-27 21:36:36
2025-07-27 21:36:36,874 - __main__ - INFO - ================================================================================
2025-07-27 21:36:36,874 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 商汤科技
2025-07-27 21:36:36,874 - __main__ - INFO - ================================================================================
2025-07-27 21:36:36,874 - __main__ - INFO - 
==================== 步骤1: 数据爬取 ====================
2025-07-27 21:36:36,874 - __main__ - INFO - 🔄 执行步骤: 数据爬取 (crawler.py)
2025-07-27 21:36:47,491 - __main__ - INFO - ✅ 成功 数据爬取 (crawler.py) - 耗时: 10.6秒 
2025-07-27 21:36:47,492 - __main__ - INFO - ✅ 步骤1 执行成功
2025-07-27 21:36:47,493 - __main__ - INFO - 
==================== 步骤2: 观点数据爬取 ====================
2025-07-27 21:36:47,493 - __main__ - INFO - 🔄 执行步骤: 观点数据爬取 (crawler_viewpoint.py)
2025-07-27 21:41:47,514 - __main__ - INFO - ❌ 失败 观点数据爬取 (crawler_viewpoint.py) - 耗时: 300.0秒 执行超时
2025-07-27 21:41:47,515 - __main__ - ERROR - 脚本执行超时: crawler_viewpoint.py
2025-07-27 21:41:47,515 - __main__ - ERROR - ❌ 步骤2 执行失败 (必需步骤)
2025-07-27 21:41:47,516 - __main__ - ERROR - 🛑 流水线执行中断
2025-07-27 21:41:47,517 - __main__ - INFO - 📋 执行报告已生成: execution_report_00020_HK_20250727_214147.md
2025-07-27 21:41:47,517 - __main__ - ERROR - 💥 研报生成流水线执行失败
2025-07-27 21:45:40,986 - __main__ - INFO - 📋 使用完整格式: 公司名称=商汤科技, 股票代码=00020.HK
2025-07-27 21:45:41,000 - __main__ - INFO - ✅ 配置加载成功
2025-07-27 21:45:41,001 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-27 21:45:41,001 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-27 21:45:41,001 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-27 21:45:41,001 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-27 21:45:41,002 - __main__ - INFO - 📊 公司名称: 商汤科技
2025-07-27 21:45:41,002 - __main__ - INFO - 📈 股票代码: 00020.HK
2025-07-27 21:45:41,002 - __main__ - INFO - ⏰ 开始时间: 2025-07-27 21:45:40
2025-07-27 21:45:41,002 - __main__ - INFO - ================================================================================
2025-07-27 21:45:41,002 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 商汤科技
2025-07-27 21:45:41,002 - __main__ - INFO - ================================================================================
2025-07-27 21:45:41,002 - __main__ - INFO - 
==================== 步骤1: 数据爬取 ====================
2025-07-27 21:45:41,003 - __main__ - INFO - 🔄 执行步骤: 数据爬取 (crawler.py)
2025-07-27 21:45:49,251 - __main__ - INFO - ✅ 成功 数据爬取 (crawler.py) - 耗时: 8.2秒 
2025-07-27 21:45:49,251 - __main__ - INFO - ✅ 步骤1 执行成功
2025-07-27 21:45:49,251 - __main__ - INFO - 
==================== 步骤2: 观点数据爬取 ====================
2025-07-27 21:45:49,251 - __main__ - INFO - 🔄 执行步骤: 观点数据爬取 (crawler_viewpoint.py)
2025-07-27 21:57:10,468 - __main__ - INFO - ✅ 成功 观点数据爬取 (crawler_viewpoint.py) - 耗时: 681.2秒 
2025-07-27 21:57:10,469 - __main__ - INFO - ✅ 步骤2 执行成功
2025-07-27 21:57:10,469 - __main__ - INFO - 
==================== 步骤3: 可视化图表生成 ====================
2025-07-27 21:57:10,469 - __main__ - INFO - 🔄 执行步骤: 可视化图表生成 (visualize_report.py)
2025-07-27 21:59:13,158 - __main__ - INFO - ✅ 成功 可视化图表生成 (visualize_report.py) - 耗时: 122.7秒 
2025-07-27 21:59:13,159 - __main__ - INFO - ✅ 步骤3 执行成功
2025-07-27 21:59:13,159 - __main__ - INFO - 
==================== 步骤4: 财务分析 ====================
2025-07-27 21:59:13,159 - __main__ - INFO - 🔄 执行步骤: 财务分析 (financial_agent.py)
2025-07-27 22:00:32,123 - __main__ - INFO - ✅ 成功 财务分析 (financial_agent.py) - 耗时: 79.0秒 
2025-07-27 22:00:32,123 - __main__ - INFO - ✅ 步骤4 执行成功
2025-07-27 22:00:32,123 - __main__ - INFO - 
==================== 步骤5: 股吧观点分析 ====================
2025-07-27 22:00:32,124 - __main__ - INFO - 🔄 执行步骤: 股吧观点分析 (guba_viewpoint_agent.py)
2025-07-27 22:00:33,514 - __main__ - INFO - ✅ 成功 股吧观点分析 (guba_viewpoint_agent.py) - 耗时: 1.4秒 
2025-07-27 22:00:33,515 - __main__ - INFO - ✅ 步骤5 执行成功
2025-07-27 22:00:33,515 - __main__ - INFO - 
==================== 步骤6: 研究报告生成 ====================
2025-07-27 22:00:33,515 - __main__ - INFO - 🔄 执行步骤: 研究报告生成 (research_report_generator.py)
2025-07-27 22:08:44,753 - __main__ - INFO - ✅ 成功 研究报告生成 (research_report_generator.py) - 耗时: 491.2秒 
2025-07-27 22:08:44,754 - __main__ - INFO - ✅ 步骤6 执行成功
2025-07-27 22:08:44,755 - __main__ - INFO - 📋 执行报告已生成: execution_report_00020_HK_20250727_220844.md
2025-07-27 22:08:44,755 - __main__ - INFO - 🎉 研报生成流水线执行成功！
2025-07-27 22:08:44,756 - __main__ - INFO - 
============================================================
2025-07-27 22:08:44,756 - __main__ - INFO - 📊 生成结果文件:
2025-07-27 22:08:44,756 - __main__ - INFO -   ✅ 财务数据: 主要指标.csv (7226字节)
2025-07-27 22:08:44,756 - __main__ - INFO -   ✅ 利润表数据: 利润表.csv (40231字节)
2025-07-27 22:08:44,756 - __main__ - INFO -   ✅ 资产负债表数据: 资产负债表.csv (57336字节)
2025-07-27 22:08:44,757 - __main__ - INFO -   ✅ 可视化图表: report_images/ (11个文件)
2025-07-27 22:08:44,757 - __main__ - INFO -   ✅ 分析报告: analysis_reports/ (8个文件)
2025-07-27 22:08:44,758 - __main__ - INFO -   ✅ 股吧观点分析: guba_viewpoint_analysis.json (89字节)
2025-07-27 22:08:44,758 - __main__ - INFO -   ✅ 最终研究报告: Company_Research_Report.docx (1232016字节)
2025-07-27 22:08:44,758 - __main__ - INFO - ============================================================
2025-07-27 22:39:53,549 - __main__ - INFO - 📋 使用完整格式: 公司名称=商汤科技, 股票代码=00020.HK
2025-07-27 22:39:53,564 - __main__ - INFO - ✅ 配置加载成功
2025-07-27 22:39:53,565 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-27 22:39:53,565 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-27 22:39:53,565 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-27 22:39:53,566 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-27 22:39:53,566 - __main__ - INFO - 📊 公司名称: 商汤科技
2025-07-27 22:39:53,566 - __main__ - INFO - 📈 股票代码: 00020.HK
2025-07-27 22:39:53,566 - __main__ - INFO - ⏰ 开始时间: 2025-07-27 22:39:53
2025-07-27 22:39:53,566 - __main__ - INFO - ⏭️  跳过步骤: 2, 3, 4, 5, 6
2025-07-27 22:39:53,566 - __main__ - INFO - ================================================================================
2025-07-27 22:39:53,568 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 商汤科技
2025-07-27 22:39:53,568 - __main__ - INFO - ================================================================================
2025-07-27 22:39:53,568 - __main__ - INFO - 
==================== 步骤1: 数据爬取 ====================
2025-07-27 22:39:53,568 - __main__ - INFO - 🔄 执行步骤: 数据爬取 (crawler.py)
2025-07-27 22:40:08,289 - __main__ - INFO - ✅ 成功 数据爬取 (crawler.py) - 耗时: 14.7秒 
2025-07-27 22:40:08,289 - __main__ - INFO - ✅ 步骤1 执行成功
2025-07-27 22:40:08,289 - __main__ - INFO - ⏭️  跳过 步骤2: 观点数据爬取
2025-07-27 22:40:08,290 - __main__ - INFO - ⏭️  跳过 步骤3: 可视化图表生成
2025-07-27 22:40:08,290 - __main__ - INFO - ⏭️  跳过 步骤4: 财务分析
2025-07-27 22:40:08,290 - __main__ - INFO - ⏭️  跳过 步骤5: 股吧观点分析
2025-07-27 22:40:08,290 - __main__ - INFO - ⏭️  跳过 步骤6: 研究报告生成
2025-07-27 22:40:08,297 - __main__ - INFO - 📋 执行报告已生成: execution_report_00020_HK_20250727_224008.md
2025-07-27 22:40:08,297 - __main__ - ERROR - 💥 研报生成流水线执行失败
2025-07-27 22:41:03,969 - __main__ - INFO - 📋 使用简化格式: 商汤科技
2025-07-27 22:41:03,983 - __main__ - INFO - ✅ 配置加载成功
2025-07-27 22:41:03,983 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-27 22:41:03,983 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-27 22:41:03,983 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-27 22:41:03,983 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-27 22:41:03,983 - __main__ - INFO - 📊 公司名称: 商汤科技
2025-07-27 22:41:03,985 - __main__ - INFO - 📈 股票代码: 00020.HK
2025-07-27 22:41:03,985 - __main__ - INFO - ⏰ 开始时间: 2025-07-27 22:41:03
2025-07-27 22:41:03,985 - __main__ - INFO - ⏭️  跳过步骤: 2, 3, 4, 5, 6
2025-07-27 22:41:03,985 - __main__ - INFO - ================================================================================
2025-07-27 22:41:03,985 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 商汤科技
2025-07-27 22:41:03,985 - __main__ - INFO - ================================================================================
2025-07-27 22:41:03,985 - __main__ - INFO - 
==================== 步骤1: 数据爬取 ====================
2025-07-27 22:41:03,986 - __main__ - INFO - 🔄 执行步骤: 数据爬取 (crawler.py)
2025-07-27 22:41:17,338 - __main__ - INFO - ✅ 成功 数据爬取 (crawler.py) - 耗时: 13.4秒 
2025-07-27 22:41:17,339 - __main__ - INFO - ✅ 步骤1 执行成功
2025-07-27 22:41:17,339 - __main__ - INFO - ⏭️  跳过 步骤2: 观点数据爬取
2025-07-27 22:41:17,339 - __main__ - INFO - ⏭️  跳过 步骤3: 可视化图表生成
2025-07-27 22:41:17,341 - __main__ - INFO - ⏭️  跳过 步骤4: 财务分析
2025-07-27 22:41:17,341 - __main__ - INFO - ⏭️  跳过 步骤5: 股吧观点分析
2025-07-27 22:41:17,341 - __main__ - INFO - ⏭️  跳过 步骤6: 研究报告生成
2025-07-27 22:41:17,342 - __main__ - INFO - 📋 执行报告已生成: execution_report_00020_HK_20250727_224117.md
2025-07-27 22:41:17,342 - __main__ - ERROR - 💥 研报生成流水线执行失败
2025-07-27 22:41:35,996 - __main__ - INFO - 📋 使用完整格式: 公司名称=商汤科技, 股票代码=00020.HK
2025-07-27 22:41:36,010 - __main__ - INFO - ✅ 配置加载成功
2025-07-27 22:41:36,010 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-27 22:41:36,010 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-27 22:41:36,011 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-27 22:41:36,011 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-27 22:41:36,011 - __main__ - INFO - 📊 公司名称: 商汤科技
2025-07-27 22:41:36,011 - __main__ - INFO - 📈 股票代码: 00020.HK
2025-07-27 22:41:36,011 - __main__ - INFO - ⏰ 开始时间: 2025-07-27 22:41:35
2025-07-27 22:41:36,011 - __main__ - INFO - ⏭️  跳过步骤: 1, 2, 3, 4, 5
2025-07-27 22:41:36,012 - __main__ - INFO - ================================================================================
2025-07-27 22:41:36,012 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 商汤科技
2025-07-27 22:41:36,012 - __main__ - INFO - ================================================================================
2025-07-27 22:41:36,012 - __main__ - INFO - ⏭️  跳过 步骤1: 数据爬取
2025-07-27 22:41:36,013 - __main__ - INFO - ⏭️  跳过 步骤2: 观点数据爬取
2025-07-27 22:41:36,013 - __main__ - INFO - ⏭️  跳过 步骤3: 可视化图表生成
2025-07-27 22:41:36,013 - __main__ - INFO - ⏭️  跳过 步骤4: 财务分析
2025-07-27 22:41:36,013 - __main__ - INFO - ⏭️  跳过 步骤5: 股吧观点分析
2025-07-27 22:41:36,013 - __main__ - INFO - 
==================== 步骤6: 研究报告生成 ====================
2025-07-27 22:41:36,014 - __main__ - INFO - 🔄 执行步骤: 研究报告生成 (research_report_generator.py)
2025-07-27 22:55:00,388 - __main__ - INFO - ✅ 成功 研究报告生成 (research_report_generator.py) - 耗时: 804.4秒 
2025-07-27 22:55:00,388 - __main__ - INFO - ✅ 步骤6 执行成功
2025-07-27 22:55:00,389 - __main__ - INFO - 📋 执行报告已生成: execution_report_00020_HK_20250727_225500.md
2025-07-27 22:55:00,390 - __main__ - ERROR - 💥 研报生成流水线执行失败
2025-07-29 22:27:20,291 - __main__ - INFO - 📋 使用完整格式: 公司名称=腾讯控股, 股票代码=00700.HK
2025-07-29 22:27:20,309 - __main__ - INFO - ✅ 配置加载成功
2025-07-29 22:27:20,309 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-29 22:27:20,309 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-29 22:27:20,310 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-29 22:27:20,310 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-29 22:27:20,311 - __main__ - INFO - 📊 公司名称: 腾讯控股
2025-07-29 22:27:20,311 - __main__ - INFO - 📈 股票代码: 00700.HK
2025-07-29 22:27:20,311 - __main__ - INFO - ⏰ 开始时间: 2025-07-29 22:27:20
2025-07-29 22:27:20,312 - __main__ - INFO - ================================================================================
2025-07-29 22:27:20,312 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 腾讯控股
2025-07-29 22:27:20,312 - __main__ - INFO - ================================================================================
2025-07-29 22:27:20,312 - __main__ - INFO - 
==================== 步骤1: 数据爬取 ====================
2025-07-29 22:27:20,312 - __main__ - INFO - 🔄 执行步骤: 数据爬取 (crawler.py)
2025-07-29 22:27:36,013 - __main__ - INFO - ✅ 成功 数据爬取 (crawler.py) - 耗时: 15.7秒 
2025-07-29 22:27:36,013 - __main__ - INFO - ✅ 步骤1 执行成功
2025-07-29 22:27:36,013 - __main__ - INFO - 
==================== 步骤2: 观点数据爬取 ====================
2025-07-29 22:27:36,014 - __main__ - INFO - 🔄 执行步骤: 观点数据爬取 (crawler_viewpoint.py)
2025-07-29 22:39:51,941 - __main__ - INFO - ✅ 成功 观点数据爬取 (crawler_viewpoint.py) - 耗时: 735.9秒 
2025-07-29 22:39:51,941 - __main__ - INFO - ✅ 步骤2 执行成功
2025-07-29 22:39:51,942 - __main__ - INFO - 
==================== 步骤3: 可视化图表生成 ====================
2025-07-29 22:39:51,942 - __main__ - INFO - 🔄 执行步骤: 可视化图表生成 (visualize_report.py)
2025-07-29 22:42:00,603 - __main__ - INFO - ✅ 成功 可视化图表生成 (visualize_report.py) - 耗时: 128.7秒 
2025-07-29 22:42:00,604 - __main__ - INFO - ✅ 步骤3 执行成功
2025-07-29 22:42:00,604 - __main__ - INFO - 
==================== 步骤4: 财务分析 ====================
2025-07-29 22:42:00,604 - __main__ - INFO - 🔄 执行步骤: 财务分析 (financial_agent.py)
2025-07-29 22:42:03,004 - __main__ - INFO - ✅ 成功 财务分析 (financial_agent.py) - 耗时: 2.4秒 
2025-07-29 22:42:03,004 - __main__ - INFO - ✅ 步骤4 执行成功
2025-07-29 22:42:03,004 - __main__ - INFO - 
==================== 步骤5: 股吧观点分析 ====================
2025-07-29 22:42:03,005 - __main__ - INFO - 🔄 执行步骤: 股吧观点分析 (guba_viewpoint_agent.py)
2025-07-29 22:57:09,597 - __main__ - INFO - ✅ 成功 股吧观点分析 (guba_viewpoint_agent.py) - 耗时: 906.6秒 
2025-07-29 22:57:09,597 - __main__ - INFO - ✅ 步骤5 执行成功
2025-07-29 22:57:09,598 - __main__ - INFO - 
==================== 步骤6: 研究报告生成 ====================
2025-07-29 22:57:09,598 - __main__ - INFO - 🔄 执行步骤: 研究报告生成 (research_report_generator.py)
2025-07-29 23:04:24,094 - __main__ - INFO - ✅ 成功 研究报告生成 (research_report_generator.py) - 耗时: 434.5秒 
2025-07-29 23:04:24,094 - __main__ - INFO - ✅ 步骤6 执行成功
2025-07-29 23:04:24,096 - __main__ - INFO - 📋 执行报告已生成: execution_report_00700_HK_20250729_230424.md
2025-07-29 23:04:24,097 - __main__ - INFO - 🎉 研报生成流水线执行成功！
2025-07-29 23:04:24,097 - __main__ - INFO - 
============================================================
2025-07-29 23:04:24,097 - __main__ - INFO - 📊 生成结果文件:
2025-07-29 23:04:24,098 - __main__ - INFO -   ✅ 财务数据: 主要指标.csv (10416字节)
2025-07-29 23:04:24,098 - __main__ - INFO -   ✅ 利润表数据: 利润表.csv (59031字节)
2025-07-29 23:04:24,098 - __main__ - INFO -   ✅ 资产负债表数据: 资产负债表.csv (124516字节)
2025-07-29 23:04:24,099 - __main__ - INFO -   ✅ 可视化图表: report_images/ (11个文件)
2025-07-29 23:04:24,099 - __main__ - INFO -   ✅ 分析报告: analysis_reports/ (9个文件)
2025-07-29 23:04:24,100 - __main__ - INFO -   ✅ 股吧观点分析: guba_viewpoint_analysis.json (59689字节)
2025-07-29 23:04:24,100 - __main__ - INFO -   ✅ 最终研究报告: Company_Research_Report.docx (1386323字节)
2025-07-29 23:04:24,100 - __main__ - INFO - ============================================================
2025-07-29 23:09:14,314 - __main__ - INFO - 📋 使用完整格式: 公司名称=中国育儿网络, 股票代码=01736.HK
2025-07-29 23:09:14,336 - __main__ - INFO - ✅ 配置加载成功
2025-07-29 23:09:14,336 - __main__ - INFO - 📋 LLM模型: deepseek/deepseek-chat-v3-0324:free
2025-07-29 23:09:14,337 - __main__ - INFO - 📋 API地址: https://openrouter.ai/api/v1
2025-07-29 23:09:14,337 - __main__ - INFO - ✅ 所有必要脚本文件检查通过
2025-07-29 23:09:14,337 - __main__ - INFO - 🚀 启动公司研报生成流水线
2025-07-29 23:09:14,337 - __main__ - INFO - 📊 公司名称: 中国育儿网络
2025-07-29 23:09:14,338 - __main__ - INFO - 📈 股票代码: 01736.HK
2025-07-29 23:09:14,338 - __main__ - INFO - ⏰ 开始时间: 2025-07-29 23:09:14
2025-07-29 23:09:14,338 - __main__ - INFO - ================================================================================
2025-07-29 23:09:14,338 - __main__ - INFO - 🎯 开始执行公司研报生成流水线: 中国育儿网络
2025-07-29 23:09:14,340 - __main__ - INFO - ================================================================================
2025-07-29 23:09:14,340 - __main__ - INFO - 
==================== 步骤1: 数据爬取 ====================
2025-07-29 23:09:14,340 - __main__ - INFO - 🔄 执行步骤: 数据爬取 (crawler.py)
2025-07-29 23:09:33,770 - __main__ - INFO - ✅ 成功 数据爬取 (crawler.py) - 耗时: 19.4秒 
2025-07-29 23:09:33,770 - __main__ - INFO - ✅ 步骤1 执行成功
2025-07-29 23:09:33,770 - __main__ - INFO - 
==================== 步骤2: 观点数据爬取 ====================
2025-07-29 23:09:33,771 - __main__ - INFO - 🔄 执行步骤: 观点数据爬取 (crawler_viewpoint.py)
2025-07-29 23:22:05,279 - __main__ - INFO - ✅ 成功 观点数据爬取 (crawler_viewpoint.py) - 耗时: 751.5秒 
2025-07-29 23:22:05,280 - __main__ - INFO - ✅ 步骤2 执行成功
2025-07-29 23:22:05,280 - __main__ - INFO - 
==================== 步骤3: 可视化图表生成 ====================
2025-07-29 23:22:05,280 - __main__ - INFO - 🔄 执行步骤: 可视化图表生成 (visualize_report.py)
2025-07-29 23:24:18,397 - __main__ - INFO - ✅ 成功 可视化图表生成 (visualize_report.py) - 耗时: 133.1秒 
2025-07-29 23:24:18,398 - __main__ - INFO - ✅ 步骤3 执行成功
2025-07-29 23:24:18,398 - __main__ - INFO - 
==================== 步骤4: 财务分析 ====================
2025-07-29 23:24:18,398 - __main__ - INFO - 🔄 执行步骤: 财务分析 (financial_agent.py)
2025-07-29 23:26:07,690 - __main__ - INFO - ✅ 成功 财务分析 (financial_agent.py) - 耗时: 109.3秒 
2025-07-29 23:26:07,691 - __main__ - INFO - ✅ 步骤4 执行成功
2025-07-29 23:26:07,691 - __main__ - INFO - 
==================== 步骤5: 股吧观点分析 ====================
2025-07-29 23:26:07,691 - __main__ - INFO - 🔄 执行步骤: 股吧观点分析 (guba_viewpoint_agent.py)
2025-07-29 23:26:09,100 - __main__ - INFO - ✅ 成功 股吧观点分析 (guba_viewpoint_agent.py) - 耗时: 1.4秒 
2025-07-29 23:26:09,101 - __main__ - INFO - ✅ 步骤5 执行成功
2025-07-29 23:26:09,101 - __main__ - INFO - 
==================== 步骤6: 研究报告生成 ====================
2025-07-29 23:26:09,101 - __main__ - INFO - 🔄 执行步骤: 研究报告生成 (research_report_generator.py)
2025-07-29 23:33:05,129 - __main__ - INFO - ✅ 成功 研究报告生成 (research_report_generator.py) - 耗时: 416.0秒 
2025-07-29 23:33:05,129 - __main__ - INFO - ✅ 步骤6 执行成功
2025-07-29 23:33:05,131 - __main__ - INFO - 📋 执行报告已生成: execution_report_01736_HK_20250729_233305.md
2025-07-29 23:33:05,131 - __main__ - INFO - 🎉 研报生成流水线执行成功！
2025-07-29 23:33:05,131 - __main__ - INFO - 
============================================================
2025-07-29 23:33:05,131 - __main__ - INFO - 📊 生成结果文件:
2025-07-29 23:33:05,132 - __main__ - INFO -   ✅ 财务数据: 主要指标.csv (9042字节)
2025-07-29 23:33:05,132 - __main__ - INFO -   ✅ 利润表数据: 利润表.csv (27030字节)
2025-07-29 23:33:05,134 - __main__ - INFO -   ✅ 资产负债表数据: 资产负债表.csv (36828字节)
2025-07-29 23:33:05,134 - __main__ - INFO -   ✅ 可视化图表: report_images/ (11个文件)
2025-07-29 23:33:05,135 - __main__ - INFO -   ✅ 分析报告: analysis_reports/ (10个文件)
2025-07-29 23:33:05,136 - __main__ - INFO -   ✅ 股吧观点分析: guba_viewpoint_analysis.json (89字节)
2025-07-29 23:33:05,136 - __main__ - INFO -   ✅ 最终研究报告: Company_Research_Report.docx (1318969字节)
2025-07-29 23:33:05,136 - __main__ - INFO - ============================================================
