@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM FinAgentX Docker 启动脚本 (Windows版本)
REM 使用方法: run.bat [命令] [参数...]

set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

:print_info
echo %BLUE%[INFO]%NC% %~1
goto :eof

:print_success
echo %GREEN%[SUCCESS]%NC% %~1
goto :eof

:print_warning
echo %YELLOW%[WARNING]%NC% %~1
goto :eof

:print_error
echo %RED%[ERROR]%NC% %~1
goto :eof

:check_docker
docker --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker 未安装，请先安装 Docker Desktop"
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker Compose 未安装，请先安装 Docker Compose"
    exit /b 1
)
goto :eof

:create_directories
call :print_info "创建必要的目录..."
if not exist "data" mkdir data
if not exist "outputs" mkdir outputs
if not exist "analysis_reports" mkdir analysis_reports
if not exist "report_images" mkdir report_images
if not exist "logs" mkdir logs
if not exist "config" mkdir config
call :print_success "目录创建完成"
goto :eof

:build_image
call :print_info "构建 FinAgentX Docker 镜像..."
docker-compose build
if errorlevel 1 (
    call :print_error "镜像构建失败"
    exit /b 1
)
call :print_success "镜像构建完成"
goto :eof

:start_services
call :print_info "启动 FinAgentX 服务..."
docker-compose up -d
if errorlevel 1 (
    call :print_error "服务启动失败"
    exit /b 1
)
call :print_success "服务启动完成"

call :print_info "等待服务就绪..."
timeout /t 10 /nobreak >nul

docker-compose ps | findstr "Up" >nul
if errorlevel 1 (
    call :print_error "服务启动失败，请检查日志"
    docker-compose logs
    exit /b 1
) else (
    call :print_success "FinAgentX 服务运行正常"
    call :print_info "可以使用以下命令查看日志:"
    echo   docker-compose logs -f finagentx
)
goto :eof

:stop_services
call :print_info "停止 FinAgentX 服务..."
docker-compose down
call :print_success "服务已停止"
goto :eof

:restart_services
call :print_info "重启 FinAgentX 服务..."
docker-compose restart
call :print_success "服务已重启"
goto :eof

:show_logs
call :print_info "显示 FinAgentX 日志..."
docker-compose logs -f finagentx
goto :eof

:enter_container
call :print_info "进入 FinAgentX 容器..."
docker-compose exec finagentx bash
goto :eof

:run_report
if "%~2"=="" (
    call :print_error "请提供公司名称或股票代码"
    echo 使用方法: run.bat report ^<公司名称或股票代码^>
    echo 示例: run.bat report 4Paradigm
    echo 示例: run.bat report 06682.HK
    exit /b 1
)

call :print_info "为 %~2 生成研报..."
docker-compose exec finagentx python run_company_research_report.py "%~2"
goto :eof

:cleanup
call :print_info "清理 Docker 资源..."
docker-compose down -v
docker system prune -f
call :print_success "清理完成"
goto :eof

:show_help
echo FinAgentX Docker 管理脚本 (Windows版本)
echo.
echo 使用方法: run.bat [命令] [参数...]
echo.
echo 命令:
echo   build     构建 Docker 镜像
echo   start     启动服务
echo   stop      停止服务
echo   restart   重启服务
echo   logs      查看日志
echo   shell     进入容器
echo   report    生成研报 (需要提供公司名称或股票代码)
echo   cleanup   清理 Docker 资源
echo   help      显示此帮助信息
echo.
echo 示例:
echo   run.bat build
echo   run.bat start
echo   run.bat report 4Paradigm
echo   run.bat report 06682.HK
echo   run.bat logs
echo.
goto :eof

:main
call :check_docker
call :create_directories

set "command=%~1"
if "%command%"=="" set "command=help"

if "%command%"=="build" (
    call :build_image
) else if "%command%"=="start" (
    call :build_image
    call :start_services
) else if "%command%"=="stop" (
    call :stop_services
) else if "%command%"=="restart" (
    call :restart_services
) else if "%command%"=="logs" (
    call :show_logs
) else if "%command%"=="shell" (
    call :enter_container
) else if "%command%"=="report" (
    call :run_report %*
) else if "%command%"=="cleanup" (
    call :cleanup
) else if "%command%"=="help" (
    call :show_help
) else if "%command%"=="--help" (
    call :show_help
) else if "%command%"=="-h" (
    call :show_help
) else (
    call :print_error "未知命令: %command%"
    call :show_help
    exit /b 1
)

goto :eof

call :main %*
