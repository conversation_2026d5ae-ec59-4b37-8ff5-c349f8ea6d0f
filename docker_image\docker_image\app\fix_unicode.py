#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复Python文件中的Unicode字符，替换为ASCII兼容的字符
"""

import os
import re

def fix_unicode_in_file(filepath):
    """修复文件中的Unicode字符"""
    # Unicode字符映射
    unicode_replacements = {
        '✅': '[成功]',
        '❌': '[失败]',
        '✓': '[成功]',
        '✗': '[失败]',
        '🔍': '[搜索]',
        '🤖': '[机器人]',
        '📊': '[图表]',
        '📈': '[趋势]',
        '💰': '[金融]',
        '🎯': '[目标]',
        '🚀': '[启动]',
        '📝': '[文档]',
        '🏢': '[公司]',
        '📅': '[日期]',
        '📤': '[发送]',
        '📋': '[清单]',
        '🎉': '[庆祝]',
        '✨': '[完成]',
        '⚠️': '[警告]',
        '✔': '[检查]',
        '🔥': '[热点]',
        '💡': '[想法]',
        '📄': '[文件]',
        '🎭': '[情绪]',
        '🧮': '[计算]',
        '🛡️': '[安全]',
        '💼': '[商业]',
        '📞': '[联系]',
        '🛠️': '[工具]',
        '🎊': '[完成]',
        '⏰': '[时间]',
        '📁': '[文件夹]',
        '🌐': '[网络]',
        '🧪': '[测试]',
        '🔧': '[配置]',
        '💻': '[电脑]',
        '📱': '[手机]',
        '🖥️': '[显示器]',
        '⭐': '[星星]',
        '🎪': '[娱乐]',
        '🎨': '[艺术]',
        '🎵': '[音乐]',
        '🎬': '[电影]',
        '🎮': '[游戏]',
        '🏆': '[奖杯]',
        '🎖️': '[奖章]',
        '🏅': '[奖牌]',
        '🎗️': '[丝带]',
        '🎀': '[蝴蝶结]',
        '🎁': '[礼物]',
        '🎂': '[蛋糕]',
        '🍰': '[甜点]',
        '🍎': '[苹果]',
        '🍊': '[橙子]',
        '🍋': '[柠檬]',
        '🍌': '[香蕉]',
        '🍉': '[西瓜]',
        '🍇': '[葡萄]',
        '🍓': '[草莓]',
        '🫐': '[蓝莓]',
        '🍈': '[甜瓜]',
        '🍒': '[樱桃]',
        '🥭': '[芒果]',
        '🍑': '[桃子]',
        '🥥': '[椰子]',
        '🥝': '[猕猴桃]',
        '🍅': '[番茄]',
        '🍆': '[茄子]',
        '🥑': '[牛油果]',
        '🥦': '[西兰花]',
        '🥬': '[生菜]',
        '🥒': '[黄瓜]',
        '🌶️': '[辣椒]',
        '🫑': '[甜椒]',
        '🌽': '[玉米]',
        '🥕': '[胡萝卜]',
        '🫒': '[橄榄]',
        '🧄': '[大蒜]',
        '🧅': '[洋葱]',
        '🥔': '[土豆]',
        '🍠': '[红薯]',
        '🥐': '[牛角包]',
        '🥖': '[法棍]',
        '🫓': '[面包]',
        '🥨': '[椒盐卷饼]',
        '🥯': '[百吉饼]',
        '🥞': '[煎饼]',
        '🧇': '[华夫饼]',
        '🧀': '[奶酪]',
        '🍖': '[肉]',
        '🍗': '[鸡腿]',
        '🥩': '[牛排]',
        '🥓': '[培根]',
        '🍔': '[汉堡]',
        '🍟': '[薯条]',
        '🍕': '[披萨]',
        '🌭': '[热狗]',
        '🥪': '[三明治]',
        '🌮': '[玉米饼]',
        '🌯': '[卷饼]',
        '🫔': '[玉米粽]',
        '🥙': '[皮塔饼]',
        '🧆': '[沙拉三明治]',
        '🥚': '[鸡蛋]',
        '🍳': '[煎蛋]',
        '🥘': '[浅锅菜]',
        '🍲': '[火锅]',
        '🫕': '[火锅]',
        '🥣': '[碗]',
        '🥗': '[沙拉]',
        '🍿': '[爆米花]',
        '🧈': '[黄油]',
        '🧂': '[盐]',
        '🥫': '[罐头]',
        '🍱': '[便当]',
        '🍘': '[米饼]',
        '🍙': '[饭团]',
        '🍚': '[米饭]',
        '🍛': '[咖喱饭]',
        '🍜': '[拉面]',
        '🍝': '[意面]',
        '🍠': '[烤红薯]',
        '🍢': '[关东煮]',
        '🍣': '[寿司]',
        '🍤': '[炸虾]',
        '🍥': '[鱼糕]',
        '🥮': '[月饼]',
        '🍡': '[团子]',
        '🥟': '[饺子]',
        '🥠': '[幸运饼干]',
        '🥡': '[外卖盒]',
        '🦀': '[螃蟹]',
        '🦞': '[龙虾]',
        '🦐': '[虾]',
        '🦑': '[鱿鱼]',
        '🐙': '[章鱼]',
        '🍦': '[软冰淇淋]',
        '🍧': '[刨冰]',
        '🍨': '[冰淇淋]',
        '🍩': '[甜甜圈]',
        '🍪': '[饼干]',
        '🎂': '[生日蛋糕]',
        '🍰': '[蛋糕]',
        '🧁': '[纸杯蛋糕]',
        '🥧': '[派]',
        '🍫': '[巧克力]',
        '🍬': '[糖果]',
        '🍭': '[棒棒糖]',
        '🍮': '[布丁]',
        '🍯': '[蜂蜜]',
        '🍼': '[奶瓶]',
        '🥛': '[牛奶]',
        '☕': '[咖啡]',
        '🫖': '[茶壶]',
        '🍵': '[茶]',
        '🍶': '[清酒]',
        '🍾': '[香槟]',
        '🍷': '[红酒]',
        '🍸': '[鸡尾酒]',
        '🍹': '[热带饮料]',
        '🍺': '[啤酒]',
        '🍻': '[干杯]',
        '🥂': '[碰杯]',
        '🥃': '[威士忌]',
        '🥤': '[饮料]',
        '🧋': '[珍珠奶茶]',
        '🧃': '[果汁盒]',
        '🧉': '[马黛茶]',
        '🧊': '[冰块]',
        '🥢': '[筷子]',
        '🍽️': '[餐具]',
        '🍴': '[刀叉]',
        '🥄': '[勺子]',
        '🔪': '[刀]',
        '🏺': '[双耳瓶]'
    }
    
    try:
        # 读取文件
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换Unicode字符
        modified = False
        for unicode_char, replacement in unicode_replacements.items():
            if unicode_char in content:
                content = content.replace(unicode_char, replacement)
                modified = True
                print(f"替换 {unicode_char} -> {replacement}")
        
        # 如果有修改，写回文件
        if modified:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"[完成] 修复文件: {filepath}")
            return True
        else:
            print(f"[跳过] 无需修复: {filepath}")
            return False
            
    except Exception as e:
        print(f"[错误] 处理文件失败 {filepath}: {e}")
        return False

def main():
    """主函数"""
    # 需要修复的文件列表
    files_to_fix = [
        'research_report_generator.py',
        'guba_viewpoint_agent.py'
    ]
    
    print("开始修复Unicode字符...")
    
    for filename in files_to_fix:
        if os.path.exists(filename):
            print(f"\n处理文件: {filename}")
            fix_unicode_in_file(filename)
        else:
            print(f"[警告] 文件不存在: {filename}")
    
    print("\n[完成] Unicode字符修复完成！")

if __name__ == "__main__":
    main()
