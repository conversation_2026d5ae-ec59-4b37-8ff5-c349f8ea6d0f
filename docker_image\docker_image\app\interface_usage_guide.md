# FinAgentX 接口使用指南

## 📋 支持的两种接口格式

FinAgentX 系统现在支持两种灵活的接口格式，您可以根据需要选择使用：

### 1. 简化格式（推荐）

**语法**: `python run_company_research_report.py <公司名称或股票代码>`

**特点**:
- 🚀 使用简单，只需一个参数
- 🧠 智能识别公司名称和股票代码
- 🔄 自动映射公司名称到对应股票代码

**示例**:
```bash
# 使用公司名称
python run_company_research_report.py 商汤科技
python run_company_research_report.py 4Paradigm
python run_company_research_report.py 腾讯控股

# 使用股票代码
python run_company_research_report.py 00020.HK
python run_company_research_report.py 06682.HK
python run_company_research_report.py 00700.HK

# 带跳过步骤
python run_company_research_report.py 商汤科技 --skip_steps 2 5
```

### 2. 完整格式（明确指定）

**语法**: `python run_company_research_report.py --company_name <公司名称> --company_code <股票代码>`

**特点**:
- 📝 明确指定公司名称和股票代码
- 🎯 适合需要精确控制的场景
- ✅ 避免自动解析可能的歧义

**示例**:
```bash
# 基本使用
python run_company_research_report.py --company_name 商汤科技 --company_code 00020.HK
python run_company_research_report.py --company_name 4Paradigm --company_code 06682.HK

# 带跳过步骤
python run_company_research_report.py --company_name 商汤科技 --company_code 00020.HK --skip_steps 2
python run_company_research_report.py --company_name 腾讯控股 --company_code 00700.HK --skip_steps 2 5
```

## 🏢 支持的公司列表

### 港股 (HK)
| 公司名称 | 股票代码 | 别名 |
|---------|---------|------|
| 商汤科技 | 00020.HK | 商汤-W, 商汤, SenseTime |
| 4Paradigm | 06682.HK | 第四范式 |
| 腾讯控股 | 00700.HK | 腾讯, Tencent |
| 阿里巴巴 | 09988.HK | 阿里, Alibaba |
| 快手 | 01024.HK | Kuaishou |
| 京东集团 | 09618.HK | 京东, JD.com |
| 美团 | 03690.HK | Meituan |

### A股 (SZ/SH)
| 公司名称 | 股票代码 | 别名 |
|---------|---------|------|
| 平安银行 | 000001.SZ | 平安 |
| 万科A | 000002.SZ | 万科 |
| 五粮液 | 000858.SZ | - |
| 浦发银行 | 600000.SH | 浦发 |
| 招商银行 | 600036.SH | 招行 |
| 贵州茅台 | 600519.SH | 茅台 |

### 美股 (US)
| 公司名称 | 股票代码 | 别名 |
|---------|---------|------|
| 苹果公司 | AAPL | 苹果, Apple |
| 微软公司 | MSFT | 微软, Microsoft |
| 特斯拉 | TSLA | Tesla |
| 谷歌 | GOOGL | Google, Alphabet |
| 亚马逊 | AMZN | Amazon |

## ⚙️ 其他参数选项

### --skip_steps
跳过指定的执行步骤，用于调试或快速测试

**可跳过的步骤**:
- `1`: 数据爬取
- `2`: 观点数据爬取（建议跳过，容易超时）
- `3`: 可视化图表生成
- `4`: 财务分析
- `5`: 股吧观点分析（可选步骤，建议跳过）
- `6`: 研究报告生成

**示例**:
```bash
# 跳过容易超时的步骤
python run_company_research_report.py 商汤科技 --skip_steps 2 5

# 只运行数据爬取和财务分析
python run_company_research_report.py 商汤科技 --skip_steps 3 4 5 6
```

### --timeout
设置单个步骤的超时时间（秒）

**示例**:
```bash
# 设置超时为5分钟
python run_company_research_report.py 商汤科技 --timeout 300
```

## 🚀 推荐使用方式

### 快速测试
```bash
# 跳过容易超时的步骤，快速生成报告
python run_company_research_report.py 商汤科技 --skip_steps 2 5
```

### 完整报告生成
```bash
# 生成完整报告（需要较长时间）
python run_company_research_report.py 商汤科技
```

### 调试特定步骤
```bash
# 只运行数据爬取步骤
python run_company_research_report.py 商汤科技 --skip_steps 2 3 4 5 6

# 只运行图表生成步骤
python run_company_research_report.py 商汤科技 --skip_steps 1 2 4 5 6
```

## ❓ 常见问题

### Q: 两种格式有什么区别？
A: 简化格式更方便，系统会自动解析；完整格式更明确，避免歧义。

### Q: 如何添加新的公司？
A: 修改 `run_company_research_report.py` 中的 `COMPANY_MAPPING` 字典。

### Q: 为什么建议跳过步骤2和5？
A: 这两个步骤涉及网络爬取，容易超时，且为可选功能。

### Q: 如何查看帮助信息？
A: 运行 `python run_company_research_report.py --help`

## 📊 输出文件

成功运行后，系统会生成以下文件：
- **数据文件**: 主要指标.csv, 利润表.csv 等
- **图表文件**: report_images/ 目录下的PNG图表
- **分析报告**: analysis_reports/ 目录下的Markdown报告
- **执行报告**: execution_report_*.md

## 🎯 最佳实践

1. **首次使用**: 建议先用简化格式测试
2. **生产环境**: 使用完整格式确保准确性
3. **调试问题**: 使用 --skip_steps 逐步排查
4. **网络环境**: 确保稳定的网络连接
5. **时间预期**: 完整流程需要15-20分钟

---

**更新时间**: 2025-07-27  
**版本**: v2.1 - 双接口格式支持版
