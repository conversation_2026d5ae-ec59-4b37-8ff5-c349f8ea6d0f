# 使用官方Python 3.9镜像作为基础镜像
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    curl \
    wget \
    git \
    fonts-dejavu-core \
    fontconfig \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements.txt文件（如果存在）
COPY docker_image/requirements.txt* ./

# 安装Python依赖
RUN pip install --no-cache-dir --upgrade pip && \
    if [ -f requirements.txt ]; then pip install --no-cache-dir -r requirements.txt; fi

# 安装常用的Python包（基于项目需求）
RUN pip install --no-cache-dir \
    requests \
    pandas \
    numpy \
    matplotlib \
    seaborn \
    plotly \
    beautifulsoup4 \
    lxml \
    openai \
    python-dotenv \
    openpyxl \
    xlrd \
    Pillow \
    aiohttp \
    asyncio

# 复制应用代码
COPY docker_image/docker_image/app/ ./

# 创建必要的目录
RUN mkdir -p analysis_reports report_images outputs logs

# 设置文件权限
RUN chmod +x *.py

# 暴露端口（如果需要Web服务）
EXPOSE 8000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import sys; sys.exit(0)" || exit 1

# 默认命令
CMD ["python", "run_company_research_report.py", "--help"]
