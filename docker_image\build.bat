@echo off
REM FinAgentX Docker 构建脚本 (Windows版本)
REM 版本: v2.1
REM 更新时间: 2025-07-29

setlocal enabledelayedexpansion

REM 项目信息
set PROJECT_NAME=finagentx
set VERSION=v2.1
set IMAGE_NAME=finagentx

echo.
echo 🚀 FinAgentX Docker 构建脚本
echo 版本: %VERSION%
echo 时间: %date% %time%
echo.

REM 检查Docker是否安装
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker 未安装，请先安装 Docker Desktop
    pause
    exit /b 1
)

REM 检查Docker是否运行
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker 服务未运行，请启动 Docker Desktop
    pause
    exit /b 1
)

echo ✅ Docker 环境检查通过

REM 检查必要文件
if not exist "Dockerfile" (
    echo ❌ 缺少必要文件: Dockerfile
    pause
    exit /b 1
)

if not exist "requirements.txt" (
    echo ❌ 缺少必要文件: requirements.txt
    pause
    exit /b 1
)

if not exist "docker_image\app\run_company_research_report.py" (
    echo ❌ 缺少必要文件: docker_image\app\run_company_research_report.py
    pause
    exit /b 1
)

echo ✅ 必要文件检查通过

REM 询问是否清理构建缓存
set /p cleanup="是否清理Docker构建缓存？(y/N): "
if /i "%cleanup%"=="y" (
    echo 🧹 清理Docker构建缓存...
    docker builder prune -f
)

REM 构建镜像
echo.
echo 🔨 开始构建Docker镜像...
echo 镜像名称: %IMAGE_NAME%:%VERSION%
echo 镜像标签: %IMAGE_NAME%:latest

REM 记录构建开始时间
set start_time=%time%

REM 执行构建
docker build ^
    --tag "%IMAGE_NAME%:%VERSION%" ^
    --tag "%IMAGE_NAME%:latest" ^
    --build-arg BUILD_DATE="%date% %time%" ^
    --build-arg VERSION="%VERSION%" ^
    .

if errorlevel 1 (
    echo ❌ Docker镜像构建失败
    pause
    exit /b 1
)

echo.
echo ✅ Docker镜像构建成功！
echo 镜像标签: %IMAGE_NAME%:%VERSION%, %IMAGE_NAME%:latest

REM 显示镜像信息
echo.
echo 📊 镜像信息:
docker images %IMAGE_NAME%

REM 运行基础测试
echo.
echo 🧪 运行基础测试...
docker run --rm %IMAGE_NAME%:latest --help >nul 2>&1
if errorlevel 1 (
    echo ⚠️  基础功能测试失败，但镜像构建成功
) else (
    echo ✅ 基础功能测试通过
)

REM 提供使用示例
echo.
echo 🎯 使用示例:
echo.
echo # 简化格式
echo docker run --rm %IMAGE_NAME%:latest 商汤科技
echo docker run --rm %IMAGE_NAME%:latest 00020.HK
echo.
echo # 完整格式
echo docker run --rm %IMAGE_NAME%:latest --company_name 商汤科技 --company_code 00020.HK
echo.
echo # 挂载输出目录
echo docker run --rm -v %cd%\outputs:/app/outputs %IMAGE_NAME%:latest 商汤科技
echo.
echo # 设置API密钥
echo docker run --rm -e OPENROUTER_API_KEY=your_api_key %IMAGE_NAME%:latest 商汤科技

REM 询问是否推送到仓库
echo.
set /p push="是否推送镜像到Docker仓库？(y/N): "
if /i "%push%"=="y" (
    set /p registry="请输入Docker仓库地址 (例: your-registry.com/finagentx): "
    if not "!registry!"=="" (
        echo 🚀 推送镜像到仓库...
        docker tag "%IMAGE_NAME%:latest" "!registry!:latest"
        docker tag "%IMAGE_NAME%:%VERSION%" "!registry!:%VERSION%"
        
        docker push "!registry!:latest"
        docker push "!registry!:%VERSION%"
        
        if errorlevel 1 (
            echo ❌ 镜像推送失败
        ) else (
            echo ✅ 镜像推送成功
        )
    )
)

echo.
echo 🎉 构建脚本执行完成！
echo 查看更多使用方法: type DOCKER_GUIDE.md
echo.
pause
