#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公司研报生成主程序
整合所有功能模块，按顺序执行完整的研报生成流程

使用方法:
python run_company_research_report.py --company_name 4Paradigm --company_code 06682.HK
python run_company_research_report.py --company_name 平安银行 --company_code 000001.SZ
python run_company_research_report.py --company_name 平安银行  # 只提供公司名称
"""

import os
import sys
import argparse
import logging
import time
from datetime import datetime
from typing import Optional, Dict, Any
import subprocess

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('research_report_generation.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class CompanyResearchReportPipeline:
    """公司研报生成流水线"""

    def __init__(self, company_name: str, company_code: str = None, skip_steps: Optional[list] = None):
        self.company_name = company_name
        self.company_code = company_code or company_name  # 如果没有提供代码，使用名称
        self.skip_steps = skip_steps or []
        self.start_time = datetime.now()
        self.execution_log = []

        # 检查配置
        self._check_config()

        # 检查必要文件
        self._check_required_files()

        logger.info(f"🚀 启动公司研报生成流水线")
        logger.info(f"📊 公司名称: {self.company_name}")
        logger.info(f"📈 股票代码: {self.company_code}")
        logger.info(f"⏰ 开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        if self.skip_steps:
            logger.info(f"⏭️  跳过步骤: {', '.join(self.skip_steps)}")
    
    def _check_config(self):
        """检查配置文件"""
        try:
            from config import config
            logger.info(f"✅ 配置加载成功")
            logger.info(f"📋 LLM模型: {config.llm.model}")
            logger.info(f"📋 API地址: {config.llm.base_url}")
        except Exception as e:
            logger.error(f"❌ 配置加载失败: {e}")
            raise

    def _check_required_files(self):
        """检查必要的脚本文件"""
        required_scripts = [
            "crawler.py",
            "crawler_viewpoint.py",
            "visualize_report.py",
            "financial_agent.py",
            "guba_viewpoint_agent.py",
            "research_report_generator.py"
        ]

        missing_files = []
        for script in required_scripts:
            if not os.path.exists(script):
                missing_files.append(script)

        if missing_files:
            logger.error(f"❌ 缺少必要的脚本文件: {', '.join(missing_files)}")
            raise FileNotFoundError(f"缺少必要文件: {missing_files}")

        logger.info("✅ 所有必要脚本文件检查通过")

def parse_company_input(company_input: str) -> tuple:
    """简化解析公司输入，返回(公司名称, 股票代码)"""
    company_input = company_input.strip()

    # 股票代码模式匹配
    import re

    # 港股代码模式 (如: 06682.HK, 00700.HK)
    hk_pattern = r'^\d{5}\.HK$'
    # A股代码模式 (如: 000001.SZ, 600036.SH)
    a_stock_pattern = r'^\d{6}\.(SZ|SH)$'
    # 美股代码模式 (如: AAPL, MSFT)
    us_stock_pattern = r'^[A-Z]{1,5}$'

    if re.match(hk_pattern, company_input.upper()):
        # 港股代码
        code = company_input.upper()
        return code, code  # 使用代码作为名称
    elif re.match(a_stock_pattern, company_input.upper()):
        # A股代码
        code = company_input.upper()
        return code, code  # 使用代码作为名称
    elif re.match(us_stock_pattern, company_input.upper()):
        # 美股代码
        code = company_input.upper()
        return code, code  # 使用代码作为名称
    else:
        # 当作公司名称处理
        return company_input, company_input  # 使用名称作为代码





class CompanyResearchReportPipeline:
    """公司研报生成流水线"""

    def __init__(self, company_name: str, company_code: str = None, skip_steps: Optional[list] = None):
        self.company_name = company_name
        self.company_code = company_code or company_name  # 如果没有提供代码，使用名称
        self.skip_steps = skip_steps or []
        self.start_time = datetime.now()
        self.execution_log = []

        # 检查配置
        self._check_config()

        # 检查必要文件
        self._check_required_files()

        logger.info(f"🚀 启动公司研报生成流水线")
        logger.info(f"📊 公司名称: {self.company_name}")
        logger.info(f"📈 股票代码: {self.company_code}")
        logger.info(f"⏰ 开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        if self.skip_steps:
            logger.info(f"⏭️  跳过步骤: {', '.join(self.skip_steps)}")

    def _check_config(self):
        """检查配置文件"""
        try:
            from config import config
            logger.info(f"✅ 配置加载成功")
            logger.info(f"📋 LLM模型: {config.llm.model}")
            logger.info(f"📋 API地址: {config.llm.base_url}")
        except Exception as e:
            logger.error(f"❌ 配置加载失败: {e}")
            raise

    def _check_required_files(self):
        """检查必要的脚本文件"""
        required_scripts = [
            "crawler.py",
            "crawler_viewpoint.py",
            "visualize_report.py",
            "financial_agent.py",
            "guba_viewpoint_agent.py",
            "research_report_generator.py"
        ]

        missing_files = []
        for script in required_scripts:
            if not os.path.exists(script):
                missing_files.append(script)

        if missing_files:
            logger.error(f"❌ 缺少必要的脚本文件: {', '.join(missing_files)}")
            raise FileNotFoundError(f"缺少必要文件: {missing_files}")

        logger.info("✅ 所有必要脚本文件检查通过")

    def _decode_with_fallback(self, byte_data: bytes) -> str:
        """使用多种编码尝试解码字节数据"""
        if not byte_data:
            return ""

        # 尝试的编码列表
        encodings = ['utf-8', 'gbk', 'gb2312', 'cp936', 'latin1']

        for encoding in encodings:
            try:
                return byte_data.decode(encoding)
            except (UnicodeDecodeError, LookupError):
                continue

        # 如果所有编码都失败，使用utf-8并忽略错误
        return byte_data.decode('utf-8', errors='replace')

    def _log_step(self, step_name: str, success: bool, duration: float, details: str = ""):
        """记录执行步骤"""
        status = "✅ 成功" if success else "❌ 失败"
        self.execution_log.append({
            'step': step_name,
            'success': success,
            'duration': duration,
            'details': details,
            'timestamp': datetime.now()
        })
        logger.info(f"{status} {step_name} - 耗时: {duration:.1f}秒 {details}")
    
    def _run_script(self, script_name: str, description: str, timeout: int = 300) -> bool:
        """运行Python脚本 - 使用二进制模式避免编码问题"""
        logger.info(f"🔄 执行步骤: {description}")
        start_time = time.time()

        try:
            # 检查脚本文件是否存在
            if not os.path.exists(script_name):
                raise FileNotFoundError(f"脚本文件不存在: {script_name}")

            # 直接使用二进制模式执行，避免subprocess内部的编码问题
            result_binary = subprocess.run(
                [sys.executable, script_name],
                capture_output=True,
                timeout=timeout
            )

            # 手动解码输出
            stdout_text = self._decode_with_fallback(result_binary.stdout)
            stderr_text = self._decode_with_fallback(result_binary.stderr)

            # 创建结果对象
            class ScriptResult:
                def __init__(self, returncode, stdout, stderr):
                    self.returncode = returncode
                    self.stdout = stdout
                    self.stderr = stderr

            result = ScriptResult(result_binary.returncode, stdout_text, stderr_text)
            
            duration = time.time() - start_time
            
            if result.returncode == 0:
                self._log_step(description, True, duration, "")
                if result.stdout:
                    logger.debug(f"输出: {result.stdout[:200]}...")
                return True
            else:
                error_msg = result.stderr or result.stdout or "未知错误"
                self._log_step(description, False, duration, f"错误: {error_msg[:100]}...")
                logger.error(f"脚本执行失败: {script_name}")
                logger.error(f"返回码: {result.returncode}")
                logger.error(f"错误信息: {error_msg}")

                # 如果有标准输出，也记录下来
                if result.stdout and result.stdout.strip():
                    logger.info(f"标准输出: {result.stdout[:500]}...")

                return False
                
        except subprocess.TimeoutExpired:
            duration = time.time() - start_time
            self._log_step(description, False, duration, "执行超时")
            logger.error(f"脚本执行超时: {script_name}")
            return False
        except Exception as e:
            duration = time.time() - start_time
            self._log_step(description, False, duration, f"异常: {str(e)}")
            logger.error(f"脚本执行异常: {script_name}, 错误: {e}")
            return False
    
    def step1_crawler(self) -> bool:
        """步骤1: 执行数据爬取"""
        return self._run_script(
            "crawler.py",
            "数据爬取 (crawler.py)",
            timeout=1800  # 30分钟超时
        )
    
    def step2_crawler_viewpoint(self) -> bool:
        """步骤2: 执行观点爬取"""
        return self._run_script(
            "crawler_viewpoint.py", 
            "观点数据爬取 (crawler_viewpoint.py)",
            timeout=1800  # 10分钟超时
        )
    
    def step3_visualize_report(self) -> bool:
        """步骤3: 生成可视化图表"""
        return self._run_script(
            "visualize_report.py",
            "可视化图表生成 (visualize_report.py)",
            timeout=900  # 15分钟超时
        )
    
    def step4_financial_agent(self) -> bool:
        """步骤4: 财务分析"""
        return self._run_script(
            "financial_agent.py",
            "财务分析 (financial_agent.py)",
            timeout=1200  # 10分钟超时
        )
    
    def step5_guba_viewpoint_agent(self) -> bool:
        """步骤5: 股吧观点分析"""
        return self._run_script(
            "guba_viewpoint_agent.py",
            "股吧观点分析 (guba_viewpoint_agent.py)",
            timeout=1200  # 10分钟超时
        )
    
    def step6_research_report_generator(self) -> bool:
        """步骤6: 生成研究报告"""
        return self._run_script(
            "research_report_generator.py",
            "研究报告生成 (research_report_generator.py)",
            timeout=1200  # 15分钟超时
        )
    
    def run_pipeline(self) -> bool:
        """运行完整的研报生成流水线"""
        logger.info("=" * 80)
        logger.info(f"🎯 开始执行公司研报生成流水线: {self.company_name}")
        logger.info("=" * 80)
        
        # 定义执行步骤
        steps = [
            ("步骤1", self.step1_crawler, "数据爬取", True),
            ("步骤2", self.step2_crawler_viewpoint, "观点数据爬取", True),
            ("步骤3", self.step3_visualize_report, "可视化图表生成", True),
            ("步骤4", self.step4_financial_agent, "财务分析", True),
            ("步骤5", self.step5_guba_viewpoint_agent, "股吧观点分析", False),  # 可选步骤
            ("步骤6", self.step6_research_report_generator, "研究报告生成", True)
        ]
        
        success_count = 0
        
        for step_num, step_func, step_desc, is_required in steps:
            # 检查是否跳过此步骤
            step_number = step_num.replace('步骤', '')
            if step_number in self.skip_steps:
                logger.info(f"⏭️  跳过 {step_num}: {step_desc}")
                continue

            logger.info(f"\n{'='*20} {step_num}: {step_desc} {'='*20}")

            try:
                success = step_func()
                if success:
                    success_count += 1
                    logger.info(f"✅ {step_num} 执行成功")
                else:
                    if is_required:
                        logger.error(f"❌ {step_num} 执行失败 (必需步骤)")
                        logger.error("🛑 流水线执行中断")
                        break
                    else:
                        logger.warning(f"⚠️  {step_num} 执行失败 (可选步骤，继续执行)")

            except Exception as e:
                logger.error(f"❌ {step_num} 执行异常: {e}")
                if is_required:
                    logger.error("🛑 流水线执行中断")
                    break
                else:
                    logger.warning("⚠️  可选步骤异常，继续执行")
        
        # 生成执行报告
        self._generate_execution_report()
        
        # 判断整体执行结果
        required_steps = sum(1 for _, _, _, is_required in steps if is_required)
        required_success = sum(1 for log in self.execution_log if log['success'])
        
        overall_success = required_success >= required_steps
        
        if overall_success:
            logger.info("🎉 研报生成流水线执行成功！")
            self._show_results()
        else:
            logger.error("💥 研报生成流水线执行失败")
        
        return overall_success
    
    def _generate_execution_report(self):
        """生成执行报告"""
        end_time = datetime.now()
        total_duration = (end_time - self.start_time).total_seconds()
        
        report = f"""
# 公司研报生成执行报告

## 基本信息
- **公司名称**: {self.company_name}
- **股票代码**: {self.company_code}
- **开始时间**: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}
- **结束时间**: {end_time.strftime('%Y-%m-%d %H:%M:%S')}
- **总耗时**: {total_duration:.1f}秒 ({total_duration/60:.1f}分钟)

## 执行步骤详情
"""
        
        for i, log in enumerate(self.execution_log, 1):
            status = "✅ 成功" if log['success'] else "❌ 失败"
            report += f"""
### {i}. {log['step']}
- **状态**: {status}
- **耗时**: {log['duration']:.1f}秒
- **时间**: {log['timestamp'].strftime('%H:%M:%S')}
- **详情**: {log['details'] or '无'}
"""
        
        # 保存报告
        safe_company_code = self.company_code.replace('.', '_').replace('/', '_')
        report_filename = f"execution_report_{safe_company_code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report)

        logger.info(f"📋 执行报告已生成: {report_filename}")
    
    def _show_results(self):
        """显示生成结果"""
        logger.info("\n" + "="*60)
        logger.info("📊 生成结果文件:")
        
        # 检查生成的文件
        result_files = [
            ("主要指标.csv", "财务数据"),
            ("利润表.csv", "利润表数据"),
            ("资产负债表.csv", "资产负债表数据"),
            ("report_images/", "可视化图表"),
            ("analysis_reports/", "分析报告"),
            ("guba_viewpoint_analysis.json", "股吧观点分析"),
            ("Company_Research_Report.docx", "最终研究报告")
        ]
        
        for filename, description in result_files:
            if os.path.exists(filename):
                if os.path.isdir(filename):
                    file_count = len([f for f in os.listdir(filename) if os.path.isfile(os.path.join(filename, f))])
                    logger.info(f"  ✅ {description}: {filename} ({file_count}个文件)")
                else:
                    file_size = os.path.getsize(filename)
                    logger.info(f"  ✅ {description}: {filename} ({file_size}字节)")
            else:
                logger.warning(f"  ⚠️  {description}: {filename} (未生成)")
        
        logger.info("="*60)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='公司研报生成主程序',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
使用示例:
  # 简化格式（自动解析）
  python run_company_research_report.py 商汤科技
  python run_company_research_report.py 00020.HK
  python run_company_research_report.py 4Paradigm

  # 完整格式（明确指定）
  python run_company_research_report.py --company_name 商汤科技 --company_code 00020.HK
  python run_company_research_report.py --company_name 4Paradigm --company_code 06682.HK

  # 跳过某些步骤
  python run_company_research_report.py 商汤科技 --skip_steps 2 5
  python run_company_research_report.py --company_name 商汤科技 --company_code 00020.HK --skip_steps 2
        '''
    )

    # 简化格式：单个位置参数（可选）
    parser.add_argument(
        'company',
        nargs='?',
        help='公司名称或股票代码（简化格式），例如: 商汤科技, 00020.HK, 4Paradigm, 06682.HK'
    )

    # 完整格式：命名参数
    parser.add_argument(
        '--company_name',
        help='公司名称（完整格式），例如: 商汤科技, 4Paradigm'
    )
    parser.add_argument(
        '--company_code',
        help='股票代码（完整格式），例如: 00020.HK, 06682.HK'
    )

    parser.add_argument(
        '--skip_steps',
        nargs='*',
        choices=['1', '2', '3', '4', '5', '6'],
        help='跳过指定步骤，例如: --skip_steps 2 5'
    )
    parser.add_argument(
        '--timeout',
        type=int,
        default=300,
        help='单个步骤超时时间（秒），默认300秒'
    )

    args = parser.parse_args()

    try:
        # 验证参数组合
        has_company = bool(args.company)
        has_name_code = bool(args.company_name or args.company_code)

        if not has_company and not has_name_code:
            parser.error("必须提供以下参数之一:\n  1. 简化格式: 公司名称或股票代码\n  2. 完整格式: --company_name 和 --company_code")

        if has_company and has_name_code:
            parser.error("不能同时使用简化格式和完整格式，请选择其中一种")

        if args.company_name and not args.company_code:
            parser.error("使用 --company_name 时必须同时提供 --company_code")
        if args.company_code and not args.company_name:
            parser.error("使用 --company_code 时必须同时提供 --company_name")

        # 解析公司信息
        if args.company:
            # 简化格式：智能解析单个参数
            company_name, company_code = parse_company_input(args.company)
            logger.info(f"📋 使用简化格式: {args.company}")
        else:
            # 完整格式：使用明确指定的参数
            company_name = args.company_name
            company_code = args.company_code
            logger.info(f"📋 使用完整格式: 公司名称={company_name}, 股票代码={company_code}")

        # 设置环境变量（供其他脚本使用）
        os.environ['COMPANY_NAME'] = company_name
        os.environ['COMPANY_CODE'] = company_code

        # 创建流水线实例
        pipeline = CompanyResearchReportPipeline(
            company_name=company_name,
            company_code=company_code,
            skip_steps=args.skip_steps
        )

        # 运行流水线
        success = pipeline.run_pipeline()

        # 返回适当的退出码
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        logger.info("🛑 用户中断执行")
        sys.exit(130)
    except Exception as e:
        logger.error(f"💥 程序执行异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
