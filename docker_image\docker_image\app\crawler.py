import requests
import pandas as pd
import json
from urllib.parse import quote, unquote
import time
import os

# 从环境变量获取目标证券代码，如果没有则使用默认值
SECUCODE = os.environ.get('COMPANY_CODE', '06682.HK')
COMPANY_NAME = os.environ.get('COMPANY_NAME', '4Paradigm')
HEADERS = {"User-Agent": "Mozilla/5.0"}

print(f"[配置] 使用公司信息: {COMPANY_NAME} ({SECUCODE})")

# 合并的中文表头映射表（包含两文件所有字段）
COLUMN_MAPPING = {
    # 基础信息
    'SECUCODE': '证券代码',
    'SECURITY_CODE': '股票代码',
    'SECURITY_NAME': '证券名称',
    'SECURITY_NAME_ABBR': '股票简称',
    'CORRE_SECURITY_NAME': '对比证券名称',
    'CORRE_SECUCODE': '对比证券代码',
    'CORRE_SECURITY_CODE': '对比股票代码',
    'TYPE_NAME': '行业类型',
    'TYPE_NAME_EN': '行业类型英文',
    'TYPE_ID': '行业ID',
    'TYPE_TYPE': '行业分类',
    'ORG_CODE': '机构代码',
    'REPORT_DATE': '报告期',
    'STD_REPORT_DATE': '标准报告期',
    'TRADE_DATE': '交易日期',
    'MAXSTDREPORTDATE': '最新报告日期',
    'DATE_TYPE_CODE': '日期类型',
    'FISCAL_YEAR': '财年',
    'START_DATE': '开始日期',
    'CURRENCY': '币种',
    'IS_CNY_CODE': '是否人民币',
    'ORG_TYPE': '机构类型',
    # 每股指标
    'BASIC_EPS': '基本每股收益',
    'DILUTED_EPS': '稀释每股收益',
    'EPS_TTM': '每股收益TTM',
    'BPS': '每股净资产',
    'BPS_NEDILUTED': '每股净资产（非稀释）',
    'PER_NETCASH_OPERATE': '每股经营现金流净额',
    'PER_OI': '每股营业收入',
    'PER_SHARES': '每股指标计算基数',
    'DPS_HKD': '每股分红港元',
    'DPS_HKD_LY': '上年每股分红港元',
    # 市值规模指标
    'HKTOTAL_MARKET_CAP': '港股总市值',
    'HKSDQMV': '港股市值',
    'TOTAL_MARKET_CAP': '总市值',
    'HKSK_MARKET_CAP': '港股市值',
    'MARKET_CAP': '市值',
    'OPERATE_INCOME': '营业收入',
    'GROSS_PROFIT': '毛利润',
    'NET_PROFIT': '净利润',
    'HOLDER_PROFIT': '归母净利润',
    'OPERATE_PROFIT': '营业利润',
    'PRETAX_PROFIT': '税前利润',
    'TOTAL_ASSETS': '总资产',
    'TOTAL_LIABILITIES': '总负债',
    'TOTAL_PARENT_EQUITY': '归属于母公司股东权益',
    'COMMON_ACS': '普通股东权益',
    # 排名指标
    'HKTOTAL_CAP_RANK': '市值排名',
    'HKSDQMV_RANK': '港股市值排名',
    'OPERATE_INCOME_RANK': '营收排名',
    'GROSS_PROFIT_RANK': '毛利排名',
    'NET_PROFIT_RANK': '净利排名',
    # 成长性指标
    'EPS_YOY': 'EPS同比增长率',
    'OPERATE_INCOME_YOY': '营业收入同比增长率',
    'OPERATE_PROFIT_YOY': '营业利润同比增长率',
    'NET_PROFIT_YOY': '净利润同比增长率',
    'GROSS_PROFIT_YOY': '毛利润同比增长率',
    'HOLDER_PROFIT_YOY': '归母净利润同比增长率',
    'TOTAL_ASSET_YOY': '总资产同比增长率',
    'OPERATE_INCOME_QOQ': '营业收入环比增长率',
    'GROSS_PROFIT_QOQ': '毛利润环比增长率',
    'HOLDER_PROFIT_QOQ': '归母净利润环比增长率',
    'EPS_YOY_RANK': 'EPS增长排名',
    'OPINCOME_YOY_RANK': '营收增长排名',
    'OPROFIT_YOY_RANK': '利润增长排名',
    'TOASSET_YOY_RANK': '资产增长排名',
    # 估值指标
    'PE_TTM': '市盈率TTM',
    'PE_LYR': '市盈率LYR',
    'PB_TTM': '市净率TTM',
    'PB_MQR': '市净率',
    'PB_LYR': '市净率LYR',
    'PS_TTM': '市销率TTM',
    'PS_LYR': '市销率LYR',
    'PCF_TTM': '市现率TTM',
    'PCE_TTM': '市现率TTM',
    'PCE_LYR': '市现率LYR',
    'PE_TTM_RANK': '市盈率排名',
    'PE_LYR_RANK': '市盈率LYR排名',
    'PB_MQR_RANK': '市净率排名',
    'PB_LYR_RANK': '市净率LYR排名',
    'PS_TTM_RANK': '市销率排名',
    'PS_LYR_RANK': '市销率LYR排名',
    'PCE_TTM_RANK': '市现率排名',
    'PCE_LYR_RANK': '市现率LYR排名',
    # 市场表现指标
    'CHANGERATE': '涨跌幅',
    'HSI_CHANGERATE': '恒指涨跌幅',
    'HSI_NAME': '恒指名称',
    'HSI_SECUCODE': '恒指代码',
    'CLOSE_PRICE': '收盘价',
    'VOLUME': '成交量',
    'AMOUNT': '成交额',
    'TURNOVER_RATE': '换手率',
    'TIME_TYPE': '时间类型',
    # 财务比率
    'ROE': '净资产收益率',
    'ROE_AVG': '加权净资产收益率',
    'ROE_YEARLY': '年化ROE',
    'ROA': '总资产回报率',
    'ROIC_YEARLY': '年化ROIC',
    'GROSS_PROFIT_RATIO': '毛利率',
    'NET_PROFIT_RATIO': '净利率',
    'CURRENT_RATIO': '流动比率',
    'QUICK_RATIO': '速动比率',
    'DEBT_ASSET_RATIO': '资产负债率',
    'DIVIDEND_YIELD': '股息收益率',
    'DIVI_RATIO': '股息支付率',
    'DIVIDEND_RATE': '股息率',
    'EQUITY_MULTIPLIER': '权益乘数',
    'EQUITY_RATIO': '权益比率',
    # 现金流指标
    'NETCASH_OPERATE': '经营活动现金流净额',
    'NETCASH_INVEST': '投资活动现金流净额',
    'NETCASH_FINANCE': '融资活动现金流净额',
    'END_CASH': '期末现金及现金等价物余额',
    'OCF_SALES': '经营现金净流量与营业收入比',
    # 周转率指标
    'ACCOUNTS_RECE_TDAYS': '应收账款周转天数',
    'INVENTORY_TDAYS': '存货周转天数',
    'CURRENT_ASSETS_TDAYS': '流动资产周转天数',
    'TOTAL_ASSETS_TDAYS': '总资产周转天数',
    # 股本结构
    'ISSUED_COMMON_SHARES': '已发行普通股数量',
    'HK_COMMON_SHARES': '港股流通股数量',
    'CURRENTDEBT_DEBT': '流动负债占比',
    # 其他
    'TAX_EBT': '所得税与利润总额比',
    'LOAN_DEPOSIT': '贷款存款比',
    'LOAN_EQUITY': '贷款股东权益比',
    'LOAN_ASSETS': '贷款资产比',
    'DEPOSIT_EQUITY': '存款股东权益比',
    'DEPOSIT_ASSETS': '存款资产比',
    'PREMIUM_INCOME': '保费收入',
    'PREMIUM_INCOME_YOY': '保费收入同比',
    'NET_INTEREST_INCOME': '净利息收入',
    'NET_INTEREST_INCOME_YOY': '净利息收入同比',
    'FEE_COMMISSION_INCOME': '手续费及佣金收入',
    'FEE_COMMISSION_INCOME_YOY': '手续费及佣金收入同比',
    # 对比期数据
    'REPORT_DATE_SQ': '报告期对比',
    'REPORT_TYPE_SQ': '报告类型对比',
    'OPERATE_INCOME_SQ': '营业收入对比',
    'OPERATE_INCOME_QOQ_SQ': '营业收入环比对比',
    'NET_PROFIT_RATIO_SQ': '净利率对比',
    'HOLDER_PROFIT_SQ': '归母净利润对比',
    'HOLDER_PROFIT_QOQ_SQ': '归母净利润环比对比',
    'ROE_AVG_SQ': '净资产收益率对比',
    'PE_TTM_SQ': '市盈率对比',
    'PB_TTM_SQ': '市净率对比',
    'ROA_SQ': '总资产回报率对比',
    'STD_ITEM_CODE': '标准科目代码',
    'STD_ITEM_NAME': '标准科目名称',
    'AMOUNT': '金额',
    'PREMIUM_EXPENSE': '已赚保费'
}

def apply_chinese_headers(df):
    """将英文字段名映射为中文"""
    chinese_columns = {}
    for col in df.columns:
        if col in COLUMN_MAPPING:
            chinese_columns[col] = COLUMN_MAPPING[col]
        else:
            chinese_columns[col] = col
    return df.rename(columns=chinese_columns)

def fetch_table(name, url, filename, max_retries=3, retry_interval=2):
    for attempt in range(max_retries):
        try:
            print(f"正在抓取：{name} (尝试{attempt+1}/{max_retries})")
            res = requests.get(url, headers=HEADERS, timeout=10)
            res.raise_for_status()
            json_data = res.json()
            data = json_data.get("result", {}).get("data", [])
            if not data:
                print(f"{name} 无数据")
                return
            df = pd.DataFrame(data)
            df = apply_chinese_headers(df)
            df.to_csv(filename, index=False, encoding="utf-8-sig")
            print(f"{name} 已保存为 {filename}")
            return
        except Exception as e:
            print(f"{name} 抓取失败：{e}")
            if attempt < max_retries - 1:
                print(f"等待{retry_interval}秒后重试...")
                time.sleep(retry_interval)
            else:
                print(f"{name} 多次抓取失败，已放弃。")

def fetch_main_indicators(secucode=SECUCODE, max_retries=3, retry_interval=2):
    print(f"正在获取主要财务指标...")
    url = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_MAININDICATOR&columns=ALL&filter=(SECUCODE=\"{secucode}\")&pageNumber=1&pageSize=20&sortTypes=-1&sortColumns=STD_REPORT_DATE&source=F10&client=PC"
    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=HEADERS, timeout=15)
            response.raise_for_status()
            data = response.json()
            if data.get("success") and data.get("result", {}).get("data"):
                result_data = data["result"]["data"]
                df = pd.DataFrame(result_data)
                df = apply_chinese_headers(df)
                core_fields = [
                    '证券代码', '股票简称', '报告期', '日期类型',
                    '总市值', '港股市值', '营业收入', '毛利润', '归母净利润', '总资产',
                    '基本每股收益', '每股净资产', '每股经营现金流净额',
                    '市盈率TTM', '市净率TTM',
                    '毛利率', '净利率', '加权净资产收益率', '总资产回报率',
                    '营业收入同比增长率', '毛利润同比增长率', '归母净利润同比增长率',
                    '营业收入环比增长率', '毛利润环比增长率', '归母净利润环比增长率',
                    '资产负债率', '流动比率', '已发行普通股数量',
                    '经营活动现金流净额', '投资活动现金流净额', '融资活动现金流净额',
                    '期末现金及现金等价物余额', '经营现金净流量与营业收入比',
                    '股息率', '股息支付率'
                ]
                available_fields = [field for field in core_fields if field in df.columns]
                df_clean = df[available_fields].copy()
                filename = "主要指标.csv"
                df_clean.to_csv(filename, index=False, encoding="utf_8_sig")
                print(f"[成功] 主要财务指标 成功获取 {len(result_data)} 条记录，保留 {len(available_fields)} 个核心字段")
                print(f"  保存为: {filename}")
                return df
            else:
                print(f"[失败] 主要财务指标 返回数据为空")
                return None
        except Exception as e:
            print(f"[失败] 主要财务指标 获取失败: {e}")
            if attempt < max_retries - 1:
                print(f"等待{retry_interval}秒后重试...")
                time.sleep(retry_interval)
            else:
                print(f"主要财务指标多次获取失败，已放弃。")
                return None

def fetch_from_exact_endpoint(url, description, max_retries=3, retry_interval=2):
    print(f"正在获取: {description}")
    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=HEADERS, timeout=15)
            response.raise_for_status()
            data = response.json()
            if data.get("success") and data.get("result", {}).get("data"):
                result_data = data["result"]["data"]
                df = pd.DataFrame(result_data)
                df = apply_chinese_headers(df)
                return df
            else:
                print(f"[失败] {description} 返回数据为空")
                return None
        except Exception as e:
            print(f"[失败] {description} 获取失败: {e}")
            if attempt < max_retries - 1:
                print(f"等待{retry_interval}秒后重试...")
                time.sleep(retry_interval)
            else:
                print(f"{description} 多次获取失败，已放弃。")
                return None

def get_target_company_data(main_indicators_df, comparison_type):
    """从主要指标中提取目标公司的特定数据"""
    if main_indicators_df is None or main_indicators_df.empty:
        return pd.DataFrame()
    target_data = main_indicators_df.iloc[0:1].copy()
    target_row = {}
    target_row['证券代码'] = SECUCODE
    target_row['股票代码'] = SECUCODE.split('.')[0]
    if '股票简称' in target_data.columns:
        target_row['对比证券名称'] = target_data['股票简称'].iloc[0]
    else:
        target_row['对比证券名称'] = '商汤-W'
    target_row['对比证券代码'] = SECUCODE
    target_row['数据来源'] = '目标公司'
    if comparison_type == '成长性':
        field_mapping = {
            '营业收入同比增长率': 'EPS同比增长率',
            '营业收入同比增长率': '营业收入同比增长率',
            '毛利润同比增长率': '营业利润同比增长率',
            '总资产': '总资产同比增长率'
        }
        for source_field, target_field in field_mapping.items():
            if source_field in target_data.columns:
                target_row[target_field] = target_data[source_field].iloc[0]
    elif comparison_type == '估值':
        field_mapping = {
            '市盈率TTM': '市盈率TTM',
            '市净率TTM': '市净率',
            '总市值': '市销率TTM',
            '港股市值': '市现率TTM'
        }
        for source_field, target_field in field_mapping.items():
            if source_field in target_data.columns:
                target_row[target_field] = target_data[source_field].iloc[0]
    elif comparison_type == '规模':
        field_mapping = {
            '港股市值': '港股市值',
            '总市值': '港股总市值',
            '营业收入': '营业收入',
            '毛利润': '毛利润',
            '总资产': '总资产'
        }
        for source_field, target_field in field_mapping.items():
            if source_field in target_data.columns:
                target_row[target_field] = target_data[source_field].iloc[0]
    if target_row:
        return pd.DataFrame([target_row])
    else:
        return pd.DataFrame()

def fetch_growth_comparison_data(secucode=SECUCODE, main_indicators_df=None):
    """获取成长性对比数据"""
    print("\n成长性对比数据")
    print("-" * 40)
    url_avg = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_PCF10_INDUSTRY_HKGROWTH&columns=SECUCODE%2CSECURITY_CODE%2CORG_CODE%2CREPORT_DATE%2CTYPE_ID%2CTYPE_TYPE%2CTYPE_NAME%2CTYPE_NAME_EN%2CCORRE_SECURITY_CODE%2CCORRE_SECUCODE%2CCORRE_SECURITY_NAME%2CEPS_YOY%2COPERATE_INCOME_YOY%2COPERATE_PROFIT_YOY%2CTOTAL_ASSET_YOY%2CEPS_YOY_RANK%2COPINCOME_YOY_RANK%2COPROFIT_YOY_RANK%2CTOASSET_YOY_RANK&quoteColumns=&filter=(SECUCODE%3D%22{secucode}%22)(CORRE_SECUCODE%3D%22%E8%A1%8C%E4%B8%9A%E5%B9%B3%E5%9D%87%22)&pageNumber=1&pageSize=&sortTypes=&sortColumns=&source=F10&client=PC&v=09061093437610773"
    df_avg = fetch_from_exact_endpoint(url_avg, "成长性-行业平均对比")
    if df_avg is not None:
        df_avg['数据来源'] = '行业平均对比'
    url_peers = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_PCF10_INDUSTRY_HKGROWTH&columns=SECUCODE%2CSECURITY_CODE%2CORG_CODE%2CREPORT_DATE%2CTYPE_ID%2CTYPE_TYPE%2CTYPE_NAME%2CTYPE_NAME_EN%2CCORRE_SECURITY_CODE%2CCORRE_SECUCODE%2CCORRE_SECURITY_NAME%2CEPS_YOY%2COPERATE_INCOME_YOY%2COPERATE_PROFIT_YOY%2CTOTAL_ASSET_YOY%2CEPS_YOY_RANK%2COPINCOME_YOY_RANK%2COPROFIT_YOY_RANK%2CTOASSET_YOY_RANK&quoteColumns=&filter=(SECUCODE%3D%22{secucode}%22)(CORRE_SECUCODE%3C%3E%22{secucode}%22)&pageNumber=1&pageSize=20&sortTypes=1&sortColumns=EPS_YOY_RANK&source=F10&client=PC&v=017880774857953075"
    df_peers = fetch_from_exact_endpoint(url_peers, "成长性-同业公司对比")
    if df_peers is not None:
        df_peers['数据来源'] = '同业公司对比'
    df_target = get_target_company_data(main_indicators_df, '成长性')
    all_dfs = []
    if df_avg is not None:
        all_dfs.append(df_avg)
    if df_peers is not None:
        all_dfs.append(df_peers)
    if not df_target.empty:
        all_dfs.append(df_target)
    if all_dfs:
        merged_df = pd.concat(all_dfs, ignore_index=True, sort=False)
        filename = "成长性对比.csv"
        merged_df.to_csv(filename, index=False, encoding="utf_8_sig")
        print(f"[成功] 成长性对比数据 合并完成: {filename} (共{len(merged_df)}条记录)")
        return merged_df
    else:
        print("[失败] 未获取到成长性对比数据")
        return None

def fetch_valuation_comparison_data(secucode=SECUCODE, main_indicators_df=None):
    """获取估值对比数据"""
    print("\n估值对比数据")
    print("-" * 40)
    url_avg = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_PCF10_INDUSTRY_HKCVALUE&columns=SECUCODE%2CSECURITY_CODE%2CORG_CODE%2CREPORT_DATE%2CTYPE_ID%2CTYPE_TYPE%2CTYPE_NAME%2CTYPE_NAME_EN%2CCORRE_SECURITY_CODE%2CCORRE_SECUCODE%2CCORRE_SECURITY_NAME%2CPE_TTM%2CPE_LYR%2CPB_MQR%2CPB_LYR%2CPS_TTM%2CPS_LYR%2CPCE_TTM%2CPCE_LYR%2CPE_TTM_RANK%2CPE_LYR_RANK%2CPB_MQR_RANK%2CPB_LYR_RANK%2CPS_TTM_RANK%2CPS_LYR_RANK%2CPCE_TTM_RANK%2CPCE_LYR_RANK&quoteColumns=&filter=(SECUCODE%3D%22{secucode}%22)(CORRE_SECUCODE%3D%22%E8%A1%8C%E4%B8%9A%E5%B9%B3%E5%9D%87%22)&pageNumber=1&pageSize=&sortTypes=&sortColumns=&source=F10&client=PC&v=02815749126560203"
    df_avg = fetch_from_exact_endpoint(url_avg, "估值-行业平均对比")
    if df_avg is not None:
        df_avg['数据来源'] = '行业平均对比'
    url_peers = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_PCF10_INDUSTRY_HKCVALUE&columns=SECUCODE%2CSECURITY_CODE%2CORG_CODE%2CREPORT_DATE%2CTYPE_ID%2CTYPE_TYPE%2CTYPE_NAME%2CTYPE_NAME_EN%2CCORRE_SECURITY_CODE%2CCORRE_SECUCODE%2CCORRE_SECURITY_NAME%2CPE_TTM%2CPE_LYR%2CPB_MQR%2CPB_LYR%2CPS_TTM%2CPS_LYR%2CPCE_TTM%2CPCE_LYR%2CPE_TTM_RANK%2CPE_LYR_RANK%2CPB_MQR_RANK%2CPB_LYR_RANK%2CPS_TTM_RANK%2CPS_LYR_RANK%2CPCE_TTM_RANK%2CPCE_LYR_RANK&quoteColumns=&filter=(SECUCODE%3D%22{secucode}%22)(CORRE_SECUCODE%3C%3E%22{secucode}%22)&pageNumber=1&pageSize=20&sortTypes=1&sortColumns=PE_TTM_RANK&source=F10&client=PC&v=0818643871070776"
    df_peers = fetch_from_exact_endpoint(url_peers, "估值-同业公司对比")
    if df_peers is not None:
        df_peers['数据来源'] = '同业公司对比'
    df_target = get_target_company_data(main_indicators_df, '估值')
    all_dfs = []
    if df_avg is not None:
        all_dfs.append(df_avg)
    if df_peers is not None:
        all_dfs.append(df_peers)
    if not df_target.empty:
        all_dfs.append(df_target)
    if all_dfs:
        merged_df = pd.concat(all_dfs, ignore_index=True, sort=False)
        filename = "估值对比.csv"
        merged_df.to_csv(filename, index=False, encoding="utf_8_sig")
        print(f"[成功] 估值对比数据 合并完成: {filename} (共{len(merged_df)}条记录)")
        return merged_df
    else:
        print("[失败] 未获取到估值对比数据")
        return None

def fetch_scale_comparison_data(secucode=SECUCODE, main_indicators_df=None):
    """获取规模对比数据"""
    print("\n规模对比数据")
    print("-" * 40)
    url_avg = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_PCF10_INDUSTRY_SCALE&columns=SECURITY_CODE%2CSECUCODE%2CTYPE_ID%2CTYPE_TYPE%2CTYPE_NAME%2CTYPE_NAME_EN%2CCORRE_SECURITY_CODE%2CCORRE_SECUCODE%2CCORRE_SECURITY_NAME%2CMAXSTDREPORTDATE%2CHKSDQMV%2CHKTOTAL_MARKET_CAP%2COPERATE_INCOME%2CGROSS_PROFIT%2CHKSDQMV_RANK%2CHKTOTAL_CAP_RANK%2COPERATE_INCOME_RANK%2CGROSS_PROFIT_RANK&quoteColumns=&filter=(SECUCODE%3D%22{secucode}%22)(CORRE_SECUCODE%3D%22%E8%A1%8C%E4%B8%9A%E5%B9%B3%E5%9D%87%22)&pageNumber=1&pageSize=&sortTypes=&sortColumns=&source=F10&client=PC&v=07222461622073554"
    df_avg = fetch_from_exact_endpoint(url_avg, "规模-行业平均对比")
    if df_avg is not None:
        df_avg['数据来源'] = '行业平均对比'
    url_peers = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_PCF10_INDUSTRY_SCALE&columns=SECURITY_CODE%2CSECUCODE%2CTYPE_ID%2CTYPE_TYPE%2CTYPE_NAME%2CTYPE_NAME_EN%2CCORRE_SECURITY_CODE%2CCORRE_SECUCODE%2CCORRE_SECURITY_NAME%2CMAXSTDREPORTDATE%2CHKSDQMV%2CHKTOTAL_MARKET_CAP%2COPERATE_INCOME%2CGROSS_PROFIT%2CHKSDQMV_RANK%2CHKTOTAL_CAP_RANK%2COPERATE_INCOME_RANK%2CGROSS_PROFIT_RANK&quoteColumns=&filter=(SECUCODE%3D%22{secucode}%22)(CORRE_SECUCODE%3C%3E%22{secucode}%22)&pageNumber=1&pageSize=50&sortTypes=1&sortColumns=HKTOTAL_CAP_RANK&source=F10&client=PC&v=037463122958889217"
    df_peers = fetch_from_exact_endpoint(url_peers, "规模-同业公司对比")
    if df_peers is not None:
        df_peers['数据来源'] = '同业公司对比'
    df_target = get_target_company_data(main_indicators_df, '规模')
    all_dfs = []
    if df_avg is not None:
        all_dfs.append(df_avg)
    if df_peers is not None:
        all_dfs.append(df_peers)
    if not df_target.empty:
        all_dfs.append(df_target)
    if all_dfs:
        merged_df = pd.concat(all_dfs, ignore_index=True, sort=False)
        filename = "规模对比.csv"
        merged_df.to_csv(filename, index=False, encoding="utf_8_sig")
        print(f"[成功] 规模对比数据 合并完成: {filename} (共{len(merged_df)}条记录)")
        return merged_df
    else:
        print("[失败] 未获取到规模对比数据")
        return None

def generate_final_summary_report(main_indicators_df, growth_df, valuation_df, scale_df):
    """生成最终汇总报告（仅返回字符串，不写入txt文件）"""
    print("\n生成最终汇总报告...（已禁用txt文件写入）")
    report = []
    report.append(f"# 商汤科技({SECUCODE}) 行业对比分析最终报告")
    report.append("=" * 70)
    report.append("")
    report.append("## 📊 数据文件概览")
    report.append("1. **主要指标.csv** - 目标公司的核心财务指标")
    if growth_df is not None:
        report.append(f"2. **成长性对比.csv** - 包含行业平均、同业公司及目标公司成长性数据 ({len(growth_df)}条记录)")
    if valuation_df is not None:
        report.append(f"3. **估值对比.csv** - 包含行业平均、同业公司及目标公司估值数据 ({len(valuation_df)}条记录)")
    if scale_df is not None:
        report.append(f"4. **规模对比.csv** - 包含行业平均、同业公司及目标公司规模数据 ({len(scale_df)}条记录)")
    report.append("")
    if main_indicators_df is not None and not main_indicators_df.empty:
        latest_data = main_indicators_df.iloc[0]
        report.append("## 🎯 目标公司核心指标 (最新期)")
        if '股票简称' in latest_data:
            report.append(f"- 公司名称: {latest_data.get('股票简称', 'N/A')}")
        if '证券代码' in latest_data:
            report.append(f"- 证券代码: {latest_data.get('证券代码', 'N/A')}")
        if '报告期' in latest_data:
            report.append(f"- 报告期: {latest_data.get('报告期', 'N/A')}")
        report.append("")
        report.append("### 规模指标")
        scale_indicators = ['总市值', '港股市值', '营业收入', '毛利润', '归母净利润', '总资产']
        for indicator in scale_indicators:
            if indicator in latest_data and pd.notna(latest_data[indicator]):
                value = latest_data[indicator]
                if value > 1e9:
                    report.append(f"- {indicator}: {value/1e9:.2f} 十亿港元")
                elif value > 1e6:
                    report.append(f"- {indicator}: {value/1e6:.2f} 百万港元")
                else:
                    report.append(f"- {indicator}: {value:,.0f} 港元")
        report.append("")
        report.append("### 估值指标")
        valuation_indicators = ['市盈率TTM', '市净率TTM', '每股收益TTM', '每股净资产']
        for indicator in valuation_indicators:
            if indicator in latest_data and pd.notna(latest_data[indicator]):
                value = latest_data[indicator]
                report.append(f"- {indicator}: {value:.2f}")
        report.append("")
        report.append("### 盈利能力指标")
        profitability_indicators = ['毛利率', '净利率', '净资产收益率', '总资产回报率']
        for indicator in profitability_indicators:
            if indicator in latest_data and pd.notna(latest_data[indicator]):
                value = latest_data[indicator]
                report.append(f"- {indicator}: {value:.2f}%")
        report.append("")
        report.append("### 成长性指标")
        growth_indicators = ['营业收入同比增长率', '毛利润同比增长率', '归母净利润同比增长率']
        for indicator in growth_indicators:
            if indicator in latest_data and pd.notna(latest_data[indicator]):
                value = latest_data[indicator]
                report.append(f"- {indicator}: {value:.2f}%")
        report.append("")
    report.append("## 📝 数据说明")
    report.append("- **数据来源**: 东方财富网F10数据")
    report.append("- **目标公司**: 从主要指标文件中提取最新财报数据")
    report.append("- **行业平均对比**: 目标公司与行业平均值的对比")
    report.append("- **同业公司对比**: 目标公司与同行业其他公司的对比")
    report.append("- **所有字段均已中文化**: 便于业务分析使用")
    report.append("")
    report.append("## 🔍 使用建议")
    report.append("1. **主要指标.csv** - 用于了解目标公司的历史财务表现")
    report.append("2. **成长性对比.csv** - 用于分析公司成长能力相对于行业的表现")
    report.append("3. **估值对比.csv** - 用于评估公司估值水平是否合理")
    report.append("4. **规模对比.csv** - 用于了解公司在行业中的规模地位")
    report.append("")
    report_text = "\n".join(report)
    # 不再写入txt文件
    return report_text

def run_financial_tables():
    """抓取资产负债表、利润表、现金流量表"""
    report_dates = [
    "2025-03-31", "2025-06-30", "2025-09-30", "2025-12-31",
    "2024-03-31", "2024-06-30", "2024-09-30", "2024-12-31",
    "2023-03-31", "2023-06-30", "2023-09-30", "2023-12-31",
    "2022-03-31", "2022-06-30", "2022-09-30", "2022-12-31",
    "2021-03-31", "2021-06-30", "2021-09-30", "2021-12-31"]
    report_filter = ",".join([f"'{d}'" for d in report_dates])
    filter_expr = f"(SECUCODE=\"{SECUCODE}\")(REPORT_DATE in ({report_filter}))"
    urls = {
        "资产负债表": f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_BALANCE_PC&columns=ALL&filter={filter_expr}&pageNumber=1&pageSize=1000&sortTypes=-1,1&sortColumns=REPORT_DATE,STD_ITEM_CODE&source=F10&client=PC",
        "利润表": f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_INCOME_PC&columns=ALL&filter={filter_expr}&pageNumber=1&pageSize=1000&sortTypes=-1,1&sortColumns=REPORT_DATE,STD_ITEM_CODE&source=F10&client=PC",
        "现金流量表": f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_CASHFLOW_PC&columns=ALL&filter={filter_expr}&pageNumber=1&pageSize=1000&sortTypes=-1,1&sortColumns=REPORT_DATE,STD_ITEM_CODE&source=F10&client=PC"
    }
    for name, url in urls.items():
        fetch_table(name, url, f"{name}.csv")

def main():
    print("开始执行最终版行业对比数据抓取...")
    print("=" * 60)
    # 1. 获取主要财务指标
    main_indicators_df = fetch_main_indicators(SECUCODE)
    time.sleep(1)
    # 2. 获取成长性对比数据（包含目标公司数据）
    growth_df = fetch_growth_comparison_data(SECUCODE, main_indicators_df)
    time.sleep(1)
    # 3. 获取估值对比数据（包含目标公司数据）
    valuation_df = fetch_valuation_comparison_data(SECUCODE, main_indicators_df)
    time.sleep(1)
    # 4. 获取规模对比数据（包含目标公司数据）
    scale_df = fetch_scale_comparison_data(SECUCODE, main_indicators_df)
    # 5. 生成最终汇总报告
    final_report = generate_final_summary_report(main_indicators_df, growth_df, valuation_df, scale_df)
    print("\n" + "=" * 60)
    print("✅ 最终版行业对比分析完成！")
    print("\n生成的核心文件:")
    print(f"  - 主要指标")
    print(f"  - 资产负债表.csv")
    print(f"  - 利润表.csv")
    print(f"  - 现金流量表.csv")
    if growth_df is not None:
        print(f"  - 成长性对比")
    if valuation_df is not None:
        print(f"  - 估值对比")
    if scale_df is not None:
        print(f"  - 规模对比")

if __name__ == "__main__":
    print("开始自动抓取所有财务表和行业对比分析...")
    print("=" * 60)
    # 1. 抓取资产负债表、利润表、现金流量表、主要指标
    run_financial_tables()
    time.sleep(1)
    # 2. 获取主要财务指标
    main_indicators_df = fetch_main_indicators(SECUCODE)
    time.sleep(1)
    # 3. 获取成长性对比数据（包含目标公司数据）
    growth_df = fetch_growth_comparison_data(SECUCODE, main_indicators_df)
    time.sleep(1)
    # 4. 获取估值对比数据（包含目标公司数据）
    valuation_df = fetch_valuation_comparison_data(SECUCODE, main_indicators_df)
    time.sleep(1)
    # 5. 获取规模对比数据（包含目标公司数据）
    scale_df = fetch_scale_comparison_data(SECUCODE, main_indicators_df)
    # 6. 生成最终汇总报告
    final_report = generate_final_summary_report(main_indicators_df, growth_df, valuation_df, scale_df)
    print("\n" + "=" * 60)
    print("[完成] 全部数据抓取与行业对比分析完成！")
    print("\n生成的核心文件:")
    print(f"  - 主要指标")
    print(f"  - 资产负债表.csv")
    print(f"  - 利润表.csv")
    print(f"  - 现金流量表.csv")
    if growth_df is not None:
        print(f"  - 成长性对比")
    if valuation_df is not None:
        print(f"  - 估值对比")
    if scale_df is not None:
        print(f"  - 规模对比")