#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财务分析Agent配置文件
配置LLM模型和相关参数
"""

import os
from dataclasses import dataclass
from typing import Optional

@dataclass
class LLMConfig:
    """LLM配置类"""
    # OpenRouter配置
    api_key: str = "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"
    base_url: str = "https://openrouter.ai/api/v1"
    model: str = "deepseek/deepseek-chat-v3-0324:free"
    
    # 请求参数
    max_tokens: int = 4000
    temperature: float = 0.1
    top_p: float = 0.9
    
    # 超时设置
    timeout: int = 60
    
    # 重试设置
    max_retries: int = 3
    retry_delay: float = 1.0

@dataclass
class AnalysisConfig:
    """分析配置类"""
    # 数据文件路径
    main_indicators_file: str = "主要指标.csv"

    # 输出配置
    output_dir: str = "analysis_reports"
    report_format: str = "markdown"

    # 分析参数
    analysis_periods: int = 5  # 分析最近几个报告期
    include_charts: bool = True

    # 报告语言
    language: str = "zh-CN"

@dataclass
class DocumentConfig:
    """文档格式配置类"""
    # 字体配置
    default_font: str = "仿宋"  # 默认字体设置为仿宋
    title_font: str = "仿宋"    # 标题字体
    content_font: str = "仿宋"  # 正文字体

    # 字体大小配置
    title_font_size: int = 16   # 标题字体大小
    heading1_font_size: int = 14 # 一级标题字体大小
    heading2_font_size: int = 13 # 二级标题字体大小
    content_font_size: int = 12  # 正文字体大小

    # 表格配置
    table_font: str = "仿宋"     # 表格字体
    table_font_size: int = 10    # 表格字体大小

    # 图表配置
    chart_caption_font: str = "仿宋"  # 图表标题字体
    chart_caption_size: int = 11      # 图表标题字体大小

@dataclass
class AppConfig:
    """应用配置类"""
    # 日志配置
    log_level: str = "INFO"
    log_file: Optional[str] = "financial_agent.log"

    # 调试模式
    debug: bool = False

    def __post_init__(self):
        self.llm = LLMConfig()
        self.analysis = AnalysisConfig()
        self.document = DocumentConfig()

# 全局配置实例
config = AppConfig()

# 环境变量覆盖
if os.getenv("OPENROUTER_API_KEY"):
    config.llm.api_key = os.getenv("OPENROUTER_API_KEY")

if os.getenv("LLM_MODEL"):
    config.llm.model = os.getenv("LLM_MODEL")

if os.getenv("DEBUG"):
    config.debug = os.getenv("DEBUG").lower() == "true"

# 验证配置
def validate_config():
    """验证配置是否有效"""
    if not config.llm.api_key:
        raise ValueError("API密钥未配置")
    
    if not config.llm.model:
        raise ValueError("模型未配置")
    
    if not os.path.exists(config.analysis.main_indicators_file):
        raise FileNotFoundError(f"数据文件不存在: {config.analysis.main_indicators_file}")
    
    # 创建输出目录
    os.makedirs(config.analysis.output_dir, exist_ok=True)
    
    return True

if __name__ == "__main__":
    # 测试配置
    try:
        validate_config()
        print("✅ 配置验证通过")
        print(f"📊 模型: {config.llm.model}")
        print(f"📁 数据文件: {config.analysis.main_indicators_file}")
        print(f"📝 输出目录: {config.analysis.output_dir}")
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
