"""
Data Collection Module for Financial Research Agent System
Handles data collection from Tushare and other financial data sources
"""
import tushare as ts
import pandas as pd
import numpy as np
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging
from config import TUSHARE_TOKEN, DATA_SOURCES

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataCollector:
    """Main data collection class for financial data"""
    
    def __init__(self):
        """Initialize Tushare API and other data sources"""
        ts.set_token(TUSHARE_TOKEN)
        self.pro = ts.pro_api()
        logger.info("Tushare API initialized successfully")
    
    def get_stock_basic_info(self, ts_code: str) -> Dict[str, Any]:
        """
        Get basic information about a stock
        
        Args:
            ts_code: Stock code (e.g., '000001.SZ')
            
        Returns:
            Dictionary with basic stock information
        """
        try:
            # Get basic stock info
            basic_info = self.pro.stock_basic(ts_code=ts_code)
            if basic_info.empty:
                logger.warning(f"No basic info found for {ts_code}")
                return {}
            
            stock_info = basic_info.iloc[0].to_dict()
            
            # Get company information
            company_info = self.pro.stock_company(ts_code=ts_code)
            if not company_info.empty:
                stock_info.update(company_info.iloc[0].to_dict())
            
            logger.info(f"Retrieved basic info for {ts_code}")
            return stock_info
            
        except Exception as e:
            logger.error(f"Error getting basic info for {ts_code}: {e}")
            return {}
    
    def get_stock_daily_data(self, ts_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        Get daily stock price data
        
        Args:
            ts_code: Stock code
            start_date: Start date (YYYYMMDD)
            end_date: End date (YYYYMMDD)
            
        Returns:
            DataFrame with daily stock data
        """
        try:
            df = self.pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)
            if df.empty:
                logger.warning(f"No daily data found for {ts_code}")
                return pd.DataFrame()
            
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            df = df.sort_values('trade_date')
            logger.info(f"Retrieved {len(df)} days of data for {ts_code}")
            return df
            
        except Exception as e:
            logger.error(f"Error getting daily data for {ts_code}: {e}")
            return pd.DataFrame()
    
    def get_financial_statements(self, ts_code: str, period: str = "20231231") -> Dict[str, pd.DataFrame]:
        """
        Get three major financial statements
        
        Args:
            ts_code: Stock code
            period: Reporting period (YYYYMMDD)
            
        Returns:
            Dictionary with balance sheet, income statement, and cash flow statement
        """
        statements = {}
        
        try:
            # Balance Sheet (资产负债表)
            balance_sheet = self.pro.balancesheet(ts_code=ts_code, period=period)
            statements['balance_sheet'] = balance_sheet
            
            # Income Statement (利润表)
            income = self.pro.income(ts_code=ts_code, period=period)
            statements['income_statement'] = income
            
            # Cash Flow Statement (现金流量表)
            cashflow = self.pro.cashflow(ts_code=ts_code, period=period)
            statements['cash_flow'] = cashflow
            
            logger.info(f"Retrieved financial statements for {ts_code}")
            return statements
            
        except Exception as e:
            logger.error(f"Error getting financial statements for {ts_code}: {e}")
            return {}
    
    def get_financial_indicators(self, ts_code: str, period: str = "20231231") -> pd.DataFrame:
        """
        Get financial indicators and ratios
        
        Args:
            ts_code: Stock code
            period: Reporting period
            
        Returns:
            DataFrame with financial indicators
        """
        try:
            indicators = self.pro.fina_indicator(ts_code=ts_code, period=period)
            if indicators.empty:
                logger.warning(f"No financial indicators found for {ts_code}")
                return pd.DataFrame()
            
            logger.info(f"Retrieved financial indicators for {ts_code}")
            return indicators
            
        except Exception as e:
            logger.error(f"Error getting financial indicators for {ts_code}: {e}")
            return pd.DataFrame()
    
    def get_industry_data(self, industry_code: str = None) -> pd.DataFrame:
        """
        Get industry classification and data
        
        Args:
            industry_code: Industry code (optional)
            
        Returns:
            DataFrame with industry data
        """
        try:
            # Get industry classification
            if industry_code:
                industry_data = self.pro.hs_const(hs_code=industry_code)
            else:
                industry_data = self.pro.industry_classify()
            
            logger.info("Retrieved industry classification data")
            return industry_data
            
        except Exception as e:
            logger.error(f"Error getting industry data: {e}")
            return pd.DataFrame()
    
    def get_macro_data(self, indicator: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        Get macroeconomic data
        
        Args:
            indicator: Macro indicator (e.g., 'GDP', 'CPI')
            start_date: Start date
            end_date: End date
            
        Returns:
            DataFrame with macro data
        """
        try:
            # Map common indicators to Tushare API calls
            indicator_map = {
                'GDP': self.pro.cn_gdp,
                'CPI': self.pro.cn_cpi,
                'PPI': self.pro.cn_ppi,
                'M2': self.pro.cn_m,
                '利率': self.pro.shibor,
                '汇率': self.pro.fx_oday
            }
            
            if indicator in indicator_map:
                if indicator in ['GDP', 'CPI', 'PPI']:
                    df = indicator_map[indicator](start_date=start_date, end_date=end_date)
                elif indicator == 'M2':
                    df = indicator_map[indicator](start_date=start_date, end_date=end_date, indicator='M2')
                else:
                    df = indicator_map[indicator]()
                    
                logger.info(f"Retrieved macro data for {indicator}")
                return df
            else:
                logger.warning(f"Indicator {indicator} not supported")
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"Error getting macro data for {indicator}: {e}")
            return pd.DataFrame()
    
    def get_index_data(self, index_code: str, start_date: str, end_date: str) -> pd.DataFrame:
        """
        Get stock index data
        
        Args:
            index_code: Index code (e.g., '000001.SH' for Shanghai Composite)
            start_date: Start date (YYYYMMDD)
            end_date: End date (YYYYMMDD)
            
        Returns:
            DataFrame with index data
        """
        try:
            df = self.pro.index_daily(ts_code=index_code, start_date=start_date, end_date=end_date)
            if df.empty:
                logger.warning(f"No index data found for {index_code}")
                return pd.DataFrame()
            
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            df = df.sort_values('trade_date')
            logger.info(f"Retrieved index data for {index_code}")
            return df
            
        except Exception as e:
            logger.error(f"Error getting index data for {index_code}: {e}")
            return pd.DataFrame()
    
    def get_market_sentiment(self, date: str = None) -> Dict[str, Any]:
        """
        Get market sentiment indicators
        
        Args:
            date: Specific date (YYYYMMDD), defaults to latest
            
        Returns:
            Dictionary with sentiment indicators
        """
        try:
            sentiment_data = {}
            
            # Get market turnover
            if date:
                turnover = self.pro.moneyflow_hsgt(trade_date=date)
            else:
                turnover = self.pro.moneyflow_hsgt()
                
            if not turnover.empty:
                sentiment_data['hsgt_flow'] = turnover.iloc[0].to_dict()
            
            # Get margin trading data
            margin_data = self.pro.margin()
            if not margin_data.empty:
                sentiment_data['margin_trading'] = margin_data.head(1).to_dict('records')[0]
            
            logger.info("Retrieved market sentiment data")
            return sentiment_data
            
        except Exception as e:
            logger.error(f"Error getting market sentiment data: {e}")
            return {}
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate technical indicators for stock data
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            DataFrame with technical indicators added
        """
        try:
            if df.empty or 'close' not in df.columns:
                return df
            
            # Moving averages
            df['MA5'] = df['close'].rolling(window=5).mean()
            df['MA10'] = df['close'].rolling(window=10).mean()
            df['MA20'] = df['close'].rolling(window=20).mean()
            df['MA60'] = df['close'].rolling(window=60).mean()
            
            # RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))
            
            # MACD
            exp1 = df['close'].ewm(span=12).mean()
            exp2 = df['close'].ewm(span=26).mean()
            df['MACD'] = exp1 - exp2
            df['Signal'] = df['MACD'].ewm(span=9).mean()
            df['Histogram'] = df['MACD'] - df['Signal']
            
            # Volatility
            df['Volatility'] = df['close'].rolling(window=20).std()
            
            logger.info("Calculated technical indicators")
            return df
            
        except Exception as e:
            logger.error(f"Error calculating technical indicators: {e}")
            return df

# Utility functions
def get_recent_trading_date(days_back: int = 1) -> str:
    """Get recent trading date in YYYYMMDD format"""
    date = datetime.now() - timedelta(days=days_back)
    return date.strftime('%Y%m%d')

def validate_ts_code(ts_code: str) -> bool:
    """Validate Tushare stock code format"""
    if not ts_code or '.' not in ts_code:
        return False
    
    code, exchange = ts_code.split('.')
    if exchange not in ['SH', 'SZ'] or len(code) != 6:
        return False
    
    return True

if __name__ == "__main__":
    # Test the data collector
    collector = DataCollector()
    
    # Test with a sample stock
    test_code = "000001.SZ"  # Ping An Bank
    
    print("Testing DataCollector...")
    
    # Get basic info
    basic_info = collector.get_stock_basic_info(test_code)
    print(f"Basic info keys: {list(basic_info.keys()) if basic_info else 'No data'}")
    
    # Get recent daily data
    end_date = get_recent_trading_date()
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
    
    daily_data = collector.get_stock_daily_data(test_code, start_date, end_date)
    print(f"Daily data shape: {daily_data.shape if not daily_data.empty else 'No data'}")
    
    print("DataCollector test completed.")