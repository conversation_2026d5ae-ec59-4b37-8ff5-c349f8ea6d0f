#!/bin/bash

# FinAgentX Docker 构建脚本
# 版本: v2.1
# 更新时间: 2025-07-29

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目信息
PROJECT_NAME="finagentx"
VERSION="v2.1"
IMAGE_NAME="finagentx"

echo -e "${BLUE}🚀 FinAgentX Docker 构建脚本${NC}"
echo -e "${BLUE}版本: ${VERSION}${NC}"
echo -e "${BLUE}时间: $(date)${NC}"
echo ""

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker 未安装，请先安装 Docker${NC}"
    exit 1
fi

# 检查Docker是否运行
if ! docker info &> /dev/null; then
    echo -e "${RED}❌ Docker 服务未运行，请启动 Docker${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker 环境检查通过${NC}"

# 检查必要文件
required_files=("Dockerfile" "requirements.txt" "docker_image/app/run_company_research_report.py")
for file in "${required_files[@]}"; do
    if [[ ! -f "$file" ]]; then
        echo -e "${RED}❌ 缺少必要文件: $file${NC}"
        exit 1
    fi
done

echo -e "${GREEN}✅ 必要文件检查通过${NC}"

# 清理旧的构建缓存（可选）
read -p "是否清理Docker构建缓存？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}🧹 清理Docker构建缓存...${NC}"
    docker builder prune -f
fi

# 构建镜像
echo -e "${BLUE}🔨 开始构建Docker镜像...${NC}"
echo -e "${BLUE}镜像名称: ${IMAGE_NAME}:${VERSION}${NC}"
echo -e "${BLUE}镜像标签: ${IMAGE_NAME}:latest${NC}"

# 记录构建开始时间
start_time=$(date +%s)

# 执行构建
if docker build \
    --tag "${IMAGE_NAME}:${VERSION}" \
    --tag "${IMAGE_NAME}:latest" \
    --build-arg BUILD_DATE="$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
    --build-arg VERSION="${VERSION}" \
    .; then
    
    # 计算构建时间
    end_time=$(date +%s)
    build_time=$((end_time - start_time))
    
    echo ""
    echo -e "${GREEN}✅ Docker镜像构建成功！${NC}"
    echo -e "${GREEN}构建时间: ${build_time}秒${NC}"
    echo -e "${GREEN}镜像标签: ${IMAGE_NAME}:${VERSION}, ${IMAGE_NAME}:latest${NC}"
    
    # 显示镜像信息
    echo ""
    echo -e "${BLUE}📊 镜像信息:${NC}"
    docker images "${IMAGE_NAME}" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    
    # 运行基础测试
    echo ""
    echo -e "${BLUE}🧪 运行基础测试...${NC}"
    if docker run --rm "${IMAGE_NAME}:latest" --help > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 基础功能测试通过${NC}"
    else
        echo -e "${YELLOW}⚠️  基础功能测试失败，但镜像构建成功${NC}"
    fi
    
    # 提供使用示例
    echo ""
    echo -e "${BLUE}🎯 使用示例:${NC}"
    echo -e "${YELLOW}# 简化格式${NC}"
    echo "docker run --rm ${IMAGE_NAME}:latest 商汤科技"
    echo "docker run --rm ${IMAGE_NAME}:latest 00020.HK"
    echo ""
    echo -e "${YELLOW}# 完整格式${NC}"
    echo "docker run --rm ${IMAGE_NAME}:latest --company_name 商汤科技 --company_code 00020.HK"
    echo ""
    echo -e "${YELLOW}# 挂载输出目录${NC}"
    echo "docker run --rm -v \$(pwd)/outputs:/app/outputs ${IMAGE_NAME}:latest 商汤科技"
    echo ""
    echo -e "${YELLOW}# 设置API密钥${NC}"
    echo "docker run --rm -e OPENROUTER_API_KEY=your_api_key ${IMAGE_NAME}:latest 商汤科技"
    
else
    echo -e "${RED}❌ Docker镜像构建失败${NC}"
    exit 1
fi

# 询问是否推送到仓库
echo ""
read -p "是否推送镜像到Docker仓库？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    read -p "请输入Docker仓库地址 (例: your-registry.com/finagentx): " registry
    if [[ -n "$registry" ]]; then
        echo -e "${BLUE}🚀 推送镜像到仓库...${NC}"
        docker tag "${IMAGE_NAME}:latest" "${registry}:latest"
        docker tag "${IMAGE_NAME}:${VERSION}" "${registry}:${VERSION}"
        
        if docker push "${registry}:latest" && docker push "${registry}:${VERSION}"; then
            echo -e "${GREEN}✅ 镜像推送成功${NC}"
        else
            echo -e "${RED}❌ 镜像推送失败${NC}"
        fi
    fi
fi

echo ""
echo -e "${GREEN}🎉 构建脚本执行完成！${NC}"
echo -e "${BLUE}查看更多使用方法: cat DOCKER_GUIDE.md${NC}"
