#!/bin/bash

# FinAgentX Docker 启动脚本
# 使用方法: ./run.sh [命令] [参数...]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
}

# 创建必要的目录
create_directories() {
    print_info "创建必要的目录..."
    mkdir -p data outputs analysis_reports report_images logs config
    print_success "目录创建完成"
}

# 构建Docker镜像
build_image() {
    print_info "构建 FinAgentX Docker 镜像..."
    docker-compose build
    print_success "镜像构建完成"
}

# 启动服务
start_services() {
    print_info "启动 FinAgentX 服务..."
    docker-compose up -d
    print_success "服务启动完成"
    
    print_info "等待服务就绪..."
    sleep 10
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        print_success "FinAgentX 服务运行正常"
        print_info "可以使用以下命令查看日志:"
        echo "  docker-compose logs -f finagentx"
    else
        print_error "服务启动失败，请检查日志"
        docker-compose logs
    fi
}

# 停止服务
stop_services() {
    print_info "停止 FinAgentX 服务..."
    docker-compose down
    print_success "服务已停止"
}

# 重启服务
restart_services() {
    print_info "重启 FinAgentX 服务..."
    docker-compose restart
    print_success "服务已重启"
}

# 查看日志
show_logs() {
    print_info "显示 FinAgentX 日志..."
    docker-compose logs -f finagentx
}

# 进入容器
enter_container() {
    print_info "进入 FinAgentX 容器..."
    docker-compose exec finagentx bash
}

# 运行研报生成
run_report() {
    if [ -z "$1" ]; then
        print_error "请提供公司名称或股票代码"
        echo "使用方法: ./run.sh report <公司名称或股票代码>"
        echo "示例: ./run.sh report 4Paradigm"
        echo "示例: ./run.sh report 06682.HK"
        exit 1
    fi
    
    print_info "为 $1 生成研报..."
    docker-compose exec finagentx python run_company_research_report.py "$1"
}

# 清理资源
cleanup() {
    print_info "清理 Docker 资源..."
    docker-compose down -v
    docker system prune -f
    print_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "FinAgentX Docker 管理脚本"
    echo ""
    echo "使用方法: ./run.sh [命令] [参数...]"
    echo ""
    echo "命令:"
    echo "  build     构建 Docker 镜像"
    echo "  start     启动服务"
    echo "  stop      停止服务"
    echo "  restart   重启服务"
    echo "  logs      查看日志"
    echo "  shell     进入容器"
    echo "  report    生成研报 (需要提供公司名称或股票代码)"
    echo "  cleanup   清理 Docker 资源"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  ./run.sh build"
    echo "  ./run.sh start"
    echo "  ./run.sh report 4Paradigm"
    echo "  ./run.sh report 06682.HK"
    echo "  ./run.sh logs"
    echo ""
}

# 主函数
main() {
    # 检查Docker环境
    check_docker
    
    # 创建必要目录
    create_directories
    
    # 根据参数执行相应操作
    case "${1:-help}" in
        build)
            build_image
            ;;
        start)
            build_image
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        logs)
            show_logs
            ;;
        shell)
            enter_container
            ;;
        report)
            shift
            run_report "$@"
            ;;
        cleanup)
            cleanup
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
