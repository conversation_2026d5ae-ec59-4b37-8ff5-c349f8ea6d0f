import os

def get_guba_stock_code(stock_code):
    if not stock_code:
        return 'hk06682'
    
    if stock_code.endswith('.HK'):
        code_num = stock_code.replace('.HK', '')
        return f'hk{code_num}'
    elif stock_code.endswith('.SZ'):
        code_num = stock_code.replace('.SZ', '')
        return f'sz{code_num}'
    elif stock_code.endswith('.SH'):
        code_num = stock_code.replace('.SH', '')
        return f'sh{code_num}'
    else:
        return 'hk06682'

# 测试
test_cases = [
    '00020.HK',
    '06682.HK', 
    '000001.SZ',
    '600036.SH'
]

for code in test_cases:
    guba_code = get_guba_stock_code(code)
    url = f"https://guba.eastmoney.com/list,{guba_code},99,j_1.html"
    print(f"{code} -> {guba_code} -> {url}")
