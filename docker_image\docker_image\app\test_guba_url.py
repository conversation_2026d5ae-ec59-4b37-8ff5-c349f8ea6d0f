#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试股吧URL生成功能
"""

import os
import sys

def get_guba_stock_code(stock_code):
    """
    根据股票代码生成股吧URL中使用的代码格式
    """
    if not stock_code:
        return 'hk06682'  # 默认值
    
    # 港股处理
    if stock_code.endswith('.HK'):
        # 去掉.HK后缀，添加hk前缀
        code_num = stock_code.replace('.HK', '')
        return f'hk{code_num}'
    
    # A股处理
    elif stock_code.endswith('.SZ'):
        # 深交所股票，去掉.SZ后缀，添加sz前缀
        code_num = stock_code.replace('.SZ', '')
        return f'sz{code_num}'
    
    elif stock_code.endswith('.SH'):
        # 上交所股票，去掉.SH后缀，添加sh前缀
        code_num = stock_code.replace('.SH', '')
        return f'sh{code_num}'
    
    # 美股或其他格式，暂时使用默认值
    else:
        print(f"[警告] 未知股票代码格式: {stock_code}，使用默认值")
        return 'hk06682'

def test_url_generation():
    """测试URL生成功能"""
    print("股吧URL生成测试")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        ('00020.HK', '商汤科技'),
        ('06682.HK', '4Paradigm'),
        ('00700.HK', '腾讯控股'),
        ('09988.HK', '阿里巴巴'),
        ('000001.SZ', '平安银行'),
        ('600036.SH', '招商银行'),
        ('600519.SH', '贵州茅台'),
    ]
    
    for stock_code, company_name in test_cases:
        guba_code = get_guba_stock_code(stock_code)
        url = f"https://guba.eastmoney.com/list,{guba_code},99,j_1.html"
        
        print(f"公司: {company_name}")
        print(f"  股票代码: {stock_code}")
        print(f"  股吧代码: {guba_code}")
        print(f"  股吧URL: {url}")
        print()

def test_with_environment():
    """测试环境变量方式"""
    print("环境变量测试")
    print("=" * 50)
    
    # 测试不同的环境变量设置
    test_envs = [
        ('商汤科技', '00020.HK'),
        ('4Paradigm', '06682.HK'),
        ('腾讯控股', '00700.HK'),
        ('平安银行', '000001.SZ'),
        ('招商银行', '600036.SH'),
    ]
    
    for company_name, stock_code in test_envs:
        # 设置环境变量
        os.environ['COMPANY_NAME'] = company_name
        os.environ['COMPANY_CODE'] = stock_code
        
        # 模拟crawler_viewpoint.py的逻辑
        STOCK_CODE = os.environ.get('COMPANY_CODE', '06682.HK')
        COMPANY_NAME = os.environ.get('COMPANY_NAME', '4Paradigm')
        GUBA_CODE = get_guba_stock_code(STOCK_CODE)
        
        url = f"https://guba.eastmoney.com/list,{GUBA_CODE},99,j_1.html"
        
        print(f"环境变量设置:")
        print(f"  COMPANY_NAME: {COMPANY_NAME}")
        print(f"  COMPANY_CODE: {STOCK_CODE}")
        print(f"  生成的股吧代码: {GUBA_CODE}")
        print(f"  生成的URL: {url}")
        print()

def main():
    """主函数"""
    print("股吧URL生成功能测试工具")
    print("=" * 60)
    
    # 基础功能测试
    test_url_generation()
    
    # 环境变量测试
    test_with_environment()
    
    print("测试完成！")
    print("\n使用说明:")
    print("1. 港股代码 (如 00020.HK) -> hk00020")
    print("2. 深交所A股 (如 000001.SZ) -> sz000001") 
    print("3. 上交所A股 (如 600036.SH) -> sh600036")
    print("4. 生成的URL格式: https://guba.eastmoney.com/list,{股吧代码},99,j_{页码}.html")

if __name__ == "__main__":
    main()
