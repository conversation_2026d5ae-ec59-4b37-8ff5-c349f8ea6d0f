# FinAgentX 智能研报生成系统 Docker镜像
# 基于Python 3.11构建，支持完整的研报生成流水线

FROM python:3.11-slim

# 维护者信息
LABEL maintainer="FinAgentX Team"
LABEL version="2.1"
LABEL description="FinAgentX 智能研报生成系统 - 基于多Agent协同的专业金融研报自动生成"

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV LANG=C.UTF-8
ENV LC_ALL=C.UTF-8
ENV TZ=Asia/Shanghai

# 安装系统依赖和中文字体
RUN apt-get update && apt-get install -y \
    # 编译工具
    gcc \
    g++ \
    make \
    # 网络工具
    curl \
    wget \
    # 版本控制
    git \
    # 中文字体支持
    fonts-dejavu-core \
    fonts-wqy-zenhei \
    fonts-wqy-microhei \
    fontconfig \
    # 图像处理依赖
    libfreetype6-dev \
    libpng-dev \
    libjpeg-dev \
    # 清理缓存
    && fc-cache -fv \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements.txt文件
COPY requirements.txt ./

# 升级pip并安装Python依赖
RUN pip install --no-cache-dir --upgrade pip setuptools wheel && \
    pip install --no-cache-dir -r requirements.txt

# 验证关键依赖安装
RUN python -c "import pandas, numpy, matplotlib, requests, openai; print('✅ 核心依赖安装成功')"

# 创建应用用户（安全最佳实践）
RUN groupadd -r finagentx && useradd -r -g finagentx finagentx

# 创建必要的目录结构
RUN mkdir -p \
    analysis_reports \
    report_images \
    outputs \
    logs \
    data \
    config \
    industry\&macro \
    __pycache__ \
    && chown -R finagentx:finagentx /app

# 复制应用代码
COPY docker_image/app/ ./

# 设置文件权限
RUN chmod +x *.py *.sh && \
    chown -R finagentx:finagentx /app

# 暴露端口（如果需要Web服务）
EXPOSE 8000

# 切换到非root用户
USER finagentx

# 健康检查 - 验证系统核心功能
HEALTHCHECK --interval=60s --timeout=30s --start-period=10s --retries=3 \
    CMD python -c "import sys, os; \
        from config import config; \
        import pandas, numpy, matplotlib, requests; \
        print('✅ FinAgentX 系统健康'); \
        sys.exit(0)" || exit 1

# 设置默认环境变量
ENV COMPANY_NAME="4Paradigm"
ENV COMPANY_CODE="06682.HK"

# 创建启动脚本
RUN echo '#!/bin/bash\n\
echo "🚀 FinAgentX 智能研报生成系统"\n\
echo "版本: v2.1 - 双接口格式 + 动态公司识别版"\n\
echo "时间: $(date)"\n\
echo "工作目录: $(pwd)"\n\
echo "Python版本: $(python --version)"\n\
echo ""\n\
if [ "$#" -eq 0 ]; then\n\
    echo "使用方法:"\n\
    echo "  docker run finagentx 商汤科技"\n\
    echo "  docker run finagentx --company_name 商汤科技 --company_code 00020.HK"\n\
    echo "  docker run finagentx 4Paradigm --skip_steps 2 5"\n\
    echo ""\n\
    python run_company_research_report.py --help\n\
else\n\
    python run_company_research_report.py "$@"\n\
fi' > /app/entrypoint.sh && chmod +x /app/entrypoint.sh

# 默认启动命令
ENTRYPOINT ["/app/entrypoint.sh"]
CMD []
