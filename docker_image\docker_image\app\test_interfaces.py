#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试两种接口格式的脚本
"""

import subprocess
import sys
import os
from datetime import datetime

def run_command(cmd, description):
    """运行命令并返回结果"""
    print(f"\n{'='*60}")
    print(f"测试: {description}")
    print(f"命令: {cmd}")
    print(f"{'='*60}")
    
    try:
        # 只测试参数解析，不实际运行（添加--help来测试参数解析）
        test_cmd = cmd + " --help"
        result = subprocess.run(
            test_cmd.split(),
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("[成功] 参数解析正常")
            # 显示帮助信息的前几行
            help_lines = result.stdout.split('\n')[:10]
            for line in help_lines:
                if line.strip():
                    print(f"  {line}")
            return True
        else:
            print(f"[失败] 参数解析错误:")
            print(f"  错误信息: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("[超时] 命令执行超时")
        return False
    except Exception as e:
        print(f"[异常] 执行异常: {e}")
        return False

def test_argument_parsing():
    """测试参数解析功能"""
    print("开始测试接口格式...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试用例
    test_cases = [
        # 简化格式测试
        {
            'cmd': 'python run_company_research_report.py 商汤科技',
            'desc': '简化格式 - 公司名称'
        },
        {
            'cmd': 'python run_company_research_report.py 00020.HK',
            'desc': '简化格式 - 港股代码'
        },
        {
            'cmd': 'python run_company_research_report.py 4Paradigm',
            'desc': '简化格式 - 英文公司名'
        },
        {
            'cmd': 'python run_company_research_report.py 06682.HK',
            'desc': '简化格式 - 港股代码2'
        },
        
        # 完整格式测试
        {
            'cmd': 'python run_company_research_report.py --company_name 商汤科技 --company_code 00020.HK',
            'desc': '完整格式 - 商汤科技'
        },
        {
            'cmd': 'python run_company_research_report.py --company_name 4Paradigm --company_code 06682.HK',
            'desc': '完整格式 - 4Paradigm'
        },
        {
            'cmd': 'python run_company_research_report.py --company_name 腾讯控股 --company_code 00700.HK',
            'desc': '完整格式 - 腾讯控股'
        },
        
        # 带跳过步骤的测试
        {
            'cmd': 'python run_company_research_report.py 商汤科技 --skip_steps 2 5',
            'desc': '简化格式 + 跳过步骤'
        },
        {
            'cmd': 'python run_company_research_report.py --company_name 商汤科技 --company_code 00020.HK --skip_steps 2',
            'desc': '完整格式 + 跳过步骤'
        }
    ]
    
    # 执行测试
    success_count = 0
    total_count = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n[{i}/{total_count}] ", end="")
        if run_command(test_case['cmd'], test_case['desc']):
            success_count += 1
    
    # 输出测试结果
    print(f"\n{'='*60}")
    print("测试结果汇总")
    print(f"{'='*60}")
    print(f"总测试数: {total_count}")
    print(f"成功数: {success_count}")
    print(f"失败数: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("\n[完成] 所有接口格式测试通过！")
        return True
    else:
        print(f"\n[警告] {total_count - success_count} 个测试失败")
        return False

def test_company_mapping():
    """测试公司映射功能"""
    print(f"\n{'='*60}")
    print("测试公司映射功能")
    print(f"{'='*60}")
    
    # 导入映射函数
    sys.path.insert(0, '.')
    try:
        from run_company_research_report import parse_company_input, get_company_name_by_code, get_company_code_by_name
        
        # 测试用例
        test_cases = [
            # 股票代码 -> 公司名称
            ('00020.HK', '商汤科技'),
            ('06682.HK', '4Paradigm'),
            ('00700.HK', '腾讯控股'),
            
            # 公司名称 -> 股票代码
            ('商汤科技', '00020.HK'),
            ('4Paradigm', '06682.HK'),
            ('腾讯控股', '00700.HK'),
            ('商汤', '00020.HK'),  # 测试别名
        ]
        
        success_count = 0
        for input_val, expected in test_cases:
            try:
                if input_val.endswith('.HK') or input_val.endswith('.SZ') or input_val.endswith('.SH') or input_val.isupper():
                    # 测试代码到名称的映射
                    result = get_company_name_by_code(input_val)
                    if expected in result or result in expected:
                        print(f"[成功] {input_val} -> {result}")
                        success_count += 1
                    else:
                        print(f"[失败] {input_val} -> {result} (期望: {expected})")
                else:
                    # 测试名称到代码的映射
                    result = get_company_code_by_name(input_val)
                    if expected == result:
                        print(f"[成功] {input_val} -> {result}")
                        success_count += 1
                    else:
                        print(f"[失败] {input_val} -> {result} (期望: {expected})")
            except Exception as e:
                print(f"[异常] {input_val}: {e}")
        
        print(f"\n映射测试结果: {success_count}/{len(test_cases)} 成功")
        return success_count == len(test_cases)
        
    except ImportError as e:
        print(f"[错误] 无法导入映射函数: {e}")
        return False

def main():
    """主函数"""
    print("FinAgentX 接口格式测试工具")
    print("="*60)
    
    # 检查当前目录
    if not os.path.exists('run_company_research_report.py'):
        print("[错误] 未找到 run_company_research_report.py 文件")
        print("请确保在正确的目录下运行此脚本")
        return False
    
    # 执行测试
    test1_result = test_company_mapping()
    test2_result = test_argument_parsing()
    
    # 最终结果
    print(f"\n{'='*60}")
    print("最终测试结果")
    print(f"{'='*60}")
    
    if test1_result and test2_result:
        print("[成功] 所有测试通过！")
        print("\n可以使用以下两种格式:")
        print("1. 简化格式: python run_company_research_report.py 商汤科技")
        print("2. 完整格式: python run_company_research_report.py --company_name 商汤科技 --company_code 00020.HK")
        return True
    else:
        print("[失败] 部分测试失败，请检查代码")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
