#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统环境检查脚本
检查运行 run_company_research_report.py 所需的各个环节是否正常
"""

import os
import sys
import importlib
import subprocess
import json
from datetime import datetime
from typing import Dict, List, Tuple

class SystemChecker:
    """系统检查器"""
    
    def __init__(self):
        self.results = []
        self.start_time = datetime.now()
        
    def log_result(self, category: str, item: str, status: bool, details: str = ""):
        """记录检查结果"""
        result = {
            'category': category,
            'item': item,
            'status': status,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.results.append(result)
        
        status_icon = "✅" if status else "❌"
        print(f"{status_icon} {category} - {item}: {details}")
        
    def check_python_environment(self):
        """检查Python环境"""
        print("\n🐍 检查Python环境...")
        
        # Python版本
        python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        version_ok = sys.version_info >= (3, 8)
        self.log_result("Python环境", "Python版本", version_ok, 
                       f"当前版本: {python_version} {'(符合要求)' if version_ok else '(需要3.8+)'}")
        
        # 编码支持
        try:
            test_str = "测试中文编码"
            test_str.encode('utf-8')
            self.log_result("Python环境", "UTF-8编码支持", True, "支持中文编码")
        except Exception as e:
            self.log_result("Python环境", "UTF-8编码支持", False, f"编码错误: {e}")
    
    def check_required_modules(self):
        """检查必需的Python模块"""
        print("\n📦 检查Python模块...")
        
        required_modules = [
            ('requests', '网络请求'),
            ('pandas', '数据处理'),
            ('numpy', '数值计算'),
            ('matplotlib', '图表绘制'),
            ('beautifulsoup4', '网页解析'),
            ('openai', 'OpenAI API'),
            ('json', 'JSON处理'),
            ('csv', 'CSV处理'),
            ('datetime', '时间处理'),
            ('logging', '日志记录'),
            ('subprocess', '子进程'),
            ('argparse', '参数解析')
        ]
        
        for module_name, description in required_modules:
            try:
                if module_name == 'beautifulsoup4':
                    importlib.import_module('bs4')
                else:
                    importlib.import_module(module_name)
                self.log_result("Python模块", description, True, f"{module_name} 已安装")
            except ImportError:
                self.log_result("Python模块", description, False, f"{module_name} 未安装")
    
    def check_config_file(self):
        """检查配置文件"""
        print("\n⚙️ 检查配置文件...")
        
        try:
            from config import config
            self.log_result("配置文件", "config.py加载", True, "配置文件加载成功")
            
            # 检查API密钥
            if hasattr(config, 'llm') and hasattr(config.llm, 'api_key'):
                api_key = config.llm.api_key
                if api_key and api_key != "your_api_key_here":
                    self.log_result("配置文件", "API密钥", True, "API密钥已配置")
                else:
                    self.log_result("配置文件", "API密钥", False, "API密钥未配置或使用默认值")
            else:
                self.log_result("配置文件", "API密钥", False, "配置结构错误")
                
            # 检查模型配置
            if hasattr(config, 'llm') and hasattr(config.llm, 'model'):
                model = config.llm.model
                self.log_result("配置文件", "LLM模型", True, f"模型: {model}")
            else:
                self.log_result("配置文件", "LLM模型", False, "模型配置缺失")
                
        except Exception as e:
            self.log_result("配置文件", "config.py加载", False, f"加载失败: {e}")
    
    def check_required_scripts(self):
        """检查必需的脚本文件"""
        print("\n📄 检查脚本文件...")
        
        required_scripts = [
            ("crawler.py", "数据爬取脚本"),
            ("crawler_viewpoint.py", "观点爬取脚本"),
            ("visualize_report.py", "图表生成脚本"),
            ("financial_agent.py", "财务分析脚本"),
            ("guba_viewpoint_agent.py", "股吧观点分析脚本"),
            ("research_report_generator.py", "研报生成脚本"),
            ("run_company_research_report.py", "主程序脚本")
        ]
        
        for script_file, description in required_scripts:
            if os.path.exists(script_file):
                # 检查文件是否可读
                try:
                    with open(script_file, 'r', encoding='utf-8') as f:
                        content = f.read(100)  # 读取前100个字符
                    self.log_result("脚本文件", description, True, f"{script_file} 存在且可读")
                except Exception as e:
                    self.log_result("脚本文件", description, False, f"{script_file} 存在但读取失败: {e}")
            else:
                self.log_result("脚本文件", description, False, f"{script_file} 不存在")
    
    def check_data_files(self):
        """检查数据文件"""
        print("\n📊 检查数据文件...")
        
        data_files = [
            ("主要指标.csv", "主要财务指标数据"),
            ("利润表.csv", "利润表数据"),
            ("资产负债表.csv", "资产负债表数据"),
            ("现金流量表.csv", "现金流量表数据")
        ]
        
        for data_file, description in data_files:
            if os.path.exists(data_file):
                try:
                    file_size = os.path.getsize(data_file)
                    self.log_result("数据文件", description, True, f"{data_file} ({file_size}字节)")
                except Exception as e:
                    self.log_result("数据文件", description, False, f"{data_file} 访问失败: {e}")
            else:
                self.log_result("数据文件", description, False, f"{data_file} 不存在")
    
    def check_output_directories(self):
        """检查输出目录"""
        print("\n📁 检查输出目录...")
        
        output_dirs = [
            ("analysis_reports", "分析报告目录"),
            ("report_images", "图表图片目录"),
            ("outputs", "通用输出目录")
        ]
        
        for dir_name, description in output_dirs:
            if os.path.exists(dir_name):
                if os.path.isdir(dir_name):
                    try:
                        # 检查目录是否可写
                        test_file = os.path.join(dir_name, "test_write.tmp")
                        with open(test_file, 'w') as f:
                            f.write("test")
                        os.remove(test_file)
                        self.log_result("输出目录", description, True, f"{dir_name} 存在且可写")
                    except Exception as e:
                        self.log_result("输出目录", description, False, f"{dir_name} 存在但不可写: {e}")
                else:
                    self.log_result("输出目录", description, False, f"{dir_name} 存在但不是目录")
            else:
                # 尝试创建目录
                try:
                    os.makedirs(dir_name, exist_ok=True)
                    self.log_result("输出目录", description, True, f"{dir_name} 已创建")
                except Exception as e:
                    self.log_result("输出目录", description, False, f"{dir_name} 创建失败: {e}")
    
    def check_network_connectivity(self):
        """检查网络连接"""
        print("\n🌐 检查网络连接...")
        
        try:
            import requests
            
            # 检查OpenRouter API连接
            try:
                response = requests.get("https://openrouter.ai", timeout=10)
                if response.status_code == 200:
                    self.log_result("网络连接", "OpenRouter API", True, "连接正常")
                else:
                    self.log_result("网络连接", "OpenRouter API", False, f"HTTP {response.status_code}")
            except Exception as e:
                self.log_result("网络连接", "OpenRouter API", False, f"连接失败: {e}")
                
        except ImportError:
            self.log_result("网络连接", "requests模块", False, "requests模块未安装")
    
    def test_command_parsing(self):
        """测试命令解析"""
        print("\n🧪 测试命令解析...")
        
        try:
            # 测试公司名称解析
            sys.path.insert(0, '.')
            from run_company_research_report import parse_company_input
            
            test_cases = [
                ("4Paradigm", "公司名称"),
                ("06682.HK", "港股代码"),
                ("000001.SZ", "A股代码"),
                ("AAPL", "美股代码")
            ]
            
            for test_input, test_type in test_cases:
                try:
                    name, code = parse_company_input(test_input)
                    self.log_result("命令解析", f"{test_type}解析", True, f"{test_input} -> {name}, {code}")
                except Exception as e:
                    self.log_result("命令解析", f"{test_type}解析", False, f"{test_input} 解析失败: {e}")
                    
        except Exception as e:
            self.log_result("命令解析", "解析功能", False, f"测试失败: {e}")
    
    def generate_report(self):
        """生成检查报告"""
        print("\n" + "="*60)
        print("📋 系统检查报告")
        print("="*60)
        
        total_checks = len(self.results)
        passed_checks = sum(1 for r in self.results if r['status'])
        failed_checks = total_checks - passed_checks
        
        print(f"总检查项: {total_checks}")
        print(f"通过: {passed_checks} ✅")
        print(f"失败: {failed_checks} ❌")
        print(f"通过率: {passed_checks/total_checks*100:.1f}%")
        
        if failed_checks > 0:
            print(f"\n❌ 失败的检查项:")
            for result in self.results:
                if not result['status']:
                    print(f"  - {result['category']} - {result['item']}: {result['details']}")
        
        # 保存详细报告
        report_file = f"system_check_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'summary': {
                        'total_checks': total_checks,
                        'passed_checks': passed_checks,
                        'failed_checks': failed_checks,
                        'pass_rate': passed_checks/total_checks*100,
                        'check_time': self.start_time.isoformat()
                    },
                    'results': self.results
                }, f, ensure_ascii=False, indent=2)
            print(f"\n📄 详细报告已保存到: {report_file}")
        except Exception as e:
            print(f"\n⚠️ 报告保存失败: {e}")
        
        return failed_checks == 0
    
    def run_all_checks(self):
        """运行所有检查"""
        print("🔍 开始系统环境检查...")
        print(f"⏰ 检查时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        self.check_python_environment()
        self.check_required_modules()
        self.check_config_file()
        self.check_required_scripts()
        self.check_data_files()
        self.check_output_directories()
        self.check_network_connectivity()
        self.test_command_parsing()
        
        return self.generate_report()

def main():
    """主函数"""
    checker = SystemChecker()
    success = checker.run_all_checks()
    
    if success:
        print(f"\n🎉 系统检查通过！可以运行以下命令:")
        print(f"python run_company_research_report.py --company_name 4Paradigm --company_code 06682.HK")
        print(f"python run_company_research_report.py 4Paradigm")
        print(f"python run_company_research_report.py 06682.HK")
        sys.exit(0)
    else:
        print(f"\n⚠️ 系统检查发现问题，请先解决上述问题后再运行主程序")
        sys.exit(1)

if __name__ == "__main__":
    main()
