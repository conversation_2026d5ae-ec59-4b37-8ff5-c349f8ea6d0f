"""
Chart Generation Module for Financial Research Agent System
Handles creation of financial charts and visualizations
"""
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime
import os
from typing import Dict, List, Optional, Any, Tuple
import logging
from config import CHART_CONFIG, REPORT_CONFIG

# Configure font support - use English to avoid font issues
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ChartGenerator:
    """Main chart generation class for financial visualizations"""
    
    def __init__(self):
        """Initialize chart generator with configuration"""
        self.output_dir = os.path.join(REPORT_CONFIG["output_dir"], "charts")
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Set style
        plt.style.use('seaborn-v0_8')
        self.colors = CHART_CONFIG["colors"]
        self.figsize = CHART_CONFIG["figsize"]
        self.dpi = CHART_CONFIG["dpi"]
        
        logger.info("Chart generator initialized")
    
    def create_stock_price_chart(
        self, 
        df: pd.DataFrame, 
        title: str = "股价走势图",
        save_path: Optional[str] = None
    ) -> str:
        """
        Create stock price trend chart with volume
        
        Args:
            df: DataFrame with columns ['trade_date', 'close', 'volume']
            title: Chart title
            save_path: Path to save chart
            
        Returns:
            Path to saved chart
        """
        try:
            if df.empty or 'close' not in df.columns:
                logger.warning("Invalid data for stock price chart")
                return ""
            
            # Create subplot with secondary y-axis
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=self.figsize, 
                                          height_ratios=[3, 1], dpi=self.dpi)
            
            # Ensure trade_date is datetime
            if 'trade_date' in df.columns:
                df['trade_date'] = pd.to_datetime(df['trade_date'])
                x = df['trade_date']
            else:
                x = range(len(df))
            
            # Price chart
            ax1.plot(x, df['close'], color=self.colors[0], linewidth=2, label='收盘价')
            
            # Add moving averages if available
            if 'MA20' in df.columns:
                ax1.plot(x, df['MA20'], color=self.colors[1], linewidth=1, 
                        alpha=0.8, label='MA20')
            if 'MA60' in df.columns:
                ax1.plot(x, df['MA60'], color=self.colors[2], linewidth=1, 
                        alpha=0.8, label='MA60')
            
            ax1.set_title(title, fontsize=14, fontweight='bold')
            ax1.set_ylabel('价格 (元)', fontsize=12)
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # Volume chart
            if 'vol' in df.columns:
                ax2.bar(x, df['vol'], color=self.colors[3], alpha=0.7, width=0.8)
                ax2.set_ylabel('成交量', fontsize=12)
                ax2.grid(True, alpha=0.3)
            
            # Format x-axis
            if 'trade_date' in df.columns:
                ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
                ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
                plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
                plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
            
            plt.tight_layout()
            
            # Save chart
            if not save_path:
                save_path = os.path.join(self.output_dir, f"stock_price_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
            
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Stock price chart saved to {save_path}")
            return save_path
            
        except Exception as e:
            logger.error(f"Error creating stock price chart: {e}")
            return ""
    
    def create_financial_ratios_chart(
        self, 
        ratios_data: Dict[str, float], 
        title: str = "财务比率分析",
        save_path: Optional[str] = None
    ) -> str:
        """
        Create financial ratios comparison chart
        
        Args:
            ratios_data: Dictionary with ratio names and values
            title: Chart title
            save_path: Path to save chart
            
        Returns:
            Path to saved chart
        """
        try:
            if not ratios_data:
                logger.warning("No data for financial ratios chart")
                return ""
            
            fig, ax = plt.subplots(figsize=self.figsize, dpi=self.dpi)
            
            # Prepare data
            ratio_names = list(ratios_data.keys())
            ratio_values = list(ratios_data.values())
            
            # Create bar chart
            bars = ax.bar(ratio_names, ratio_values, color=self.colors[:len(ratio_names)])
            
            # Add value labels on bars
            for bar, value in zip(bars, ratio_values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{value:.2f}', ha='center', va='bottom')
            
            ax.set_title(title, fontsize=14, fontweight='bold')
            ax.set_ylabel('比率值', fontsize=12)
            ax.grid(True, alpha=0.3, axis='y')
            
            # Rotate x-axis labels if needed
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
            
            plt.tight_layout()
            
            # Save chart
            if not save_path:
                save_path = os.path.join(self.output_dir, f"financial_ratios_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
            
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Financial ratios chart saved to {save_path}")
            return save_path
            
        except Exception as e:
            logger.error(f"Error creating financial ratios chart: {e}")
            return ""
    
    def create_industry_comparison_chart(
        self, 
        comparison_data: pd.DataFrame,
        metric: str,
        title: str = "行业对比分析",
        save_path: Optional[str] = None
    ) -> str:
        """
        Create industry comparison chart
        
        Args:
            comparison_data: DataFrame with companies/industries and metrics
            metric: Metric to compare
            title: Chart title
            save_path: Path to save chart
            
        Returns:
            Path to saved chart
        """
        try:
            if comparison_data.empty or metric not in comparison_data.columns:
                logger.warning("Invalid data for industry comparison chart")
                return ""
            
            fig, ax = plt.subplots(figsize=self.figsize, dpi=self.dpi)
            
            # Sort data by metric value
            comparison_data = comparison_data.sort_values(metric, ascending=True)
            
            # Create horizontal bar chart
            bars = ax.barh(comparison_data.index, comparison_data[metric], 
                          color=self.colors[0])
            
            # Add value labels
            for i, (idx, row) in enumerate(comparison_data.iterrows()):
                ax.text(row[metric], i, f'{row[metric]:.2f}', 
                       va='center', ha='left' if row[metric] >= 0 else 'right')
            
            ax.set_title(title, fontsize=14, fontweight='bold')
            ax.set_xlabel(metric, fontsize=12)
            ax.grid(True, alpha=0.3, axis='x')
            
            plt.tight_layout()
            
            # Save chart
            if not save_path:
                save_path = os.path.join(self.output_dir, f"industry_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
            
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Industry comparison chart saved to {save_path}")
            return save_path
            
        except Exception as e:
            logger.error(f"Error creating industry comparison chart: {e}")
            return ""
    
    def create_macro_indicators_chart(
        self, 
        macro_data: Dict[str, pd.DataFrame],
        title: str = "宏观经济指标",
        save_path: Optional[str] = None
    ) -> str:
        """
        Create macroeconomic indicators chart
        
        Args:
            macro_data: Dictionary with indicator names and DataFrames
            title: Chart title
            save_path: Path to save chart
            
        Returns:
            Path to saved chart
        """
        try:
            if not macro_data:
                logger.warning("No data for macro indicators chart")
                return ""
            
            # Create subplots based on number of indicators
            n_indicators = len(macro_data)
            rows = (n_indicators + 1) // 2
            cols = 2 if n_indicators > 1 else 1
            
            fig, axes = plt.subplots(rows, cols, figsize=(self.figsize[0]*cols, self.figsize[1]*rows), 
                                   dpi=self.dpi)
            if n_indicators == 1:
                axes = [axes]
            elif rows == 1:
                axes = axes if isinstance(axes, list) else [axes]
            else:
                axes = axes.flatten()
            
            for i, (indicator, df) in enumerate(macro_data.items()):
                if i >= len(axes):
                    break
                    
                ax = axes[i]
                
                if df.empty:
                    continue
                
                # Assume first column is date, second is value
                date_col = df.columns[0]
                value_col = df.columns[1] if len(df.columns) > 1 else df.columns[0]
                
                if date_col != value_col:
                    x = pd.to_datetime(df[date_col])
                    y = df[value_col]
                else:
                    x = range(len(df))
                    y = df[value_col]
                
                ax.plot(x, y, color=self.colors[i % len(self.colors)], 
                       linewidth=2, marker='o', markersize=4)
                
                ax.set_title(indicator, fontsize=12, fontweight='bold')
                ax.grid(True, alpha=0.3)
                
                if isinstance(x[0], pd.Timestamp):
                    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
                    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
            
            # Hide unused subplots
            for i in range(len(macro_data), len(axes)):
                axes[i].set_visible(False)
            
            plt.suptitle(title, fontsize=16, fontweight='bold')
            plt.tight_layout()
            
            # Save chart
            if not save_path:
                save_path = os.path.join(self.output_dir, f"macro_indicators_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
            
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Macro indicators chart saved to {save_path}")
            return save_path
            
        except Exception as e:
            logger.error(f"Error creating macro indicators chart: {e}")
            return ""
    
    def create_plotly_interactive_chart(
        self, 
        df: pd.DataFrame,
        chart_type: str = "candlestick",
        title: str = "交互式图表",
        save_path: Optional[str] = None
    ) -> str:
        """
        Create interactive chart using Plotly
        
        Args:
            df: DataFrame with OHLCV data
            chart_type: Type of chart (candlestick, line, etc.)
            title: Chart title
            save_path: Path to save HTML file
            
        Returns:
            Path to saved HTML file
        """
        try:
            if df.empty:
                logger.warning("No data for interactive chart")
                return ""
            
            if chart_type == "candlestick" and all(col in df.columns for col in ['open', 'high', 'low', 'close']):
                # Candlestick chart
                fig = go.Figure(data=go.Candlestick(
                    x=df.index,
                    open=df['open'],
                    high=df['high'],
                    low=df['low'],
                    close=df['close'],
                    name="K线"
                ))
                
                # Add volume subplot
                if 'vol' in df.columns:
                    fig = make_subplots(
                        rows=2, cols=1,
                        shared_xaxes=True,
                        vertical_spacing=0.1,
                        subplot_titles=('价格', '成交量'),
                        row_width=[0.7, 0.3]
                    )
                    
                    fig.add_trace(go.Candlestick(
                        x=df.index,
                        open=df['open'],
                        high=df['high'], 
                        low=df['low'],
                        close=df['close'],
                        name="K线"
                    ), row=1, col=1)
                    
                    fig.add_trace(go.Bar(
                        x=df.index,
                        y=df['vol'],
                        name="成交量",
                        marker_color='lightblue'
                    ), row=2, col=1)
            
            else:
                # Line chart fallback
                fig = go.Figure()
                if 'close' in df.columns:
                    fig.add_trace(go.Scatter(
                        x=df.index,
                        y=df['close'],
                        mode='lines',
                        name='收盘价',
                        line=dict(color=self.colors[0], width=2)
                    ))
            
            fig.update_layout(
                title=title,
                xaxis_title="日期",
                yaxis_title="价格",
                template="plotly_white",
                height=600
            )
            
            # Save as HTML
            if not save_path:
                save_path = os.path.join(self.output_dir, f"interactive_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html")
            
            fig.write_html(save_path)
            
            logger.info(f"Interactive chart saved to {save_path}")
            return save_path
            
        except Exception as e:
            logger.error(f"Error creating interactive chart: {e}")
            return ""
    
    def create_correlation_heatmap(
        self, 
        correlation_matrix: pd.DataFrame,
        title: str = "相关性热力图",
        save_path: Optional[str] = None
    ) -> str:
        """
        Create correlation heatmap
        
        Args:
            correlation_matrix: Correlation matrix DataFrame
            title: Chart title
            save_path: Path to save chart
            
        Returns:
            Path to saved chart
        """
        try:
            if correlation_matrix.empty:
                logger.warning("No data for correlation heatmap")
                return ""
            
            fig, ax = plt.subplots(figsize=self.figsize, dpi=self.dpi)
            
            # Create heatmap
            sns.heatmap(correlation_matrix, 
                       annot=True, 
                       cmap='RdYlBu_r', 
                       center=0,
                       square=True,
                       fmt='.2f',
                       cbar_kws={"shrink": .8},
                       ax=ax)
            
            ax.set_title(title, fontsize=14, fontweight='bold')
            
            plt.tight_layout()
            
            # Save chart
            if not save_path:
                save_path = os.path.join(self.output_dir, f"correlation_heatmap_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
            
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Correlation heatmap saved to {save_path}")
            return save_path
            
        except Exception as e:
            logger.error(f"Error creating correlation heatmap: {e}")
            return ""
    
    def create_pie_chart(
        self, 
        data: Dict[str, float],
        title: str = "构成分析",
        save_path: Optional[str] = None
    ) -> str:
        """
        Create pie chart for composition analysis
        
        Args:
            data: Dictionary with labels and values
            title: Chart title
            save_path: Path to save chart
            
        Returns:
            Path to saved chart
        """
        try:
            if not data:
                logger.warning("No data for pie chart")
                return ""
            
            fig, ax = plt.subplots(figsize=self.figsize, dpi=self.dpi)
            
            labels = list(data.keys())
            values = list(data.values())
            
            # Create pie chart
            wedges, texts, autotexts = ax.pie(values, labels=labels, 
                                             colors=self.colors[:len(labels)],
                                             autopct='%1.1f%%',
                                             startangle=90)
            
            # Enhance text appearance
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
            
            ax.set_title(title, fontsize=14, fontweight='bold')
            
            plt.tight_layout()
            
            # Save chart
            if not save_path:
                save_path = os.path.join(self.output_dir, f"pie_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
            
            plt.savefig(save_path, dpi=self.dpi, bbox_inches='tight')
            plt.close()
            
            logger.info(f"Pie chart saved to {save_path}")
            return save_path
            
        except Exception as e:
            logger.error(f"Error creating pie chart: {e}")
            return ""

def create_chart_summary(chart_paths: List[str]) -> str:
    """
    Create summary description of generated charts
    
    Args:
        chart_paths: List of paths to generated charts
        
    Returns:
        Text summary of charts
    """
    if not chart_paths:
        return "未生成图表"
    
    summary_parts = []
    for i, path in enumerate(chart_paths, 1):
        filename = os.path.basename(path)
        chart_type = filename.split('_')[0] if '_' in filename else "图表"
        summary_parts.append(f"{i}. {chart_type}: {filename}")
    
    return f"本报告包含{len(chart_paths)}个图表：\n" + "\n".join(summary_parts)

    def create_mermaid_chart(
        self,
        mermaid_code: str,
        title: str = "Mermaid Chart",
        save_path: Optional[str] = None
    ) -> str:
        """
        Create chart using Mermaid syntax and save as image
        
        Args:
            mermaid_code: Mermaid diagram code
            title: Chart title
            save_path: Path to save chart
            
        Returns:
            Path to saved chart
        """
        try:
            # Create HTML content with Mermaid
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
                <script>mermaid.initialize({{startOnLoad:true}});</script>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    h2 {{ text-align: center; color: #333; }}
                    .mermaid {{ text-align: center; }}
                </style>
            </head>
            <body>
                <h2>{title}</h2>
                <div class="mermaid">
                {mermaid_code}
                </div>
            </body>
            </html>
            """
            
            if not save_path:
                save_path = os.path.join(self.output_dir, f"mermaid_chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html")
            
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"Mermaid chart saved to {save_path}")
            return save_path
            
        except Exception as e:
            logger.error(f"Error creating Mermaid chart: {e}")
            return ""
    
    def create_industry_chain_diagram(
        self,
        industry: str = "智能风控&征信服务",
        save_path: Optional[str] = None
    ) -> str:
        """
        Create industry value chain diagram using Mermaid
        
        Args:
            industry: Industry name
            save_path: Path to save chart
            
        Returns:
            Path to saved chart
        """
        mermaid_code = """
        graph TD
            A[Data Providers<br/>数据提供商] --> B[Risk Control Platform<br/>风控平台]
            B --> C[Financial Institutions<br/>金融机构]
            B --> D[E-commerce<br/>电商平台]
            B --> E[Fintech Companies<br/>金融科技公司]
            
            F[Government Agencies<br/>政府机构] --> A
            G[Telecom Operators<br/>运营商] --> A
            H[Internet Companies<br/>互联网公司] --> A
            
            C --> I[Banks<br/>银行]
            C --> J[Insurance<br/>保险]
            C --> K[Securities<br/>证券]
            
            D --> L[Online Lending<br/>网络借贷]
            D --> M[Payment Services<br/>支付服务]
            
            E --> N[Credit Scoring<br/>信用评分]
            E --> O[Anti-fraud<br/>反欺诈]
            
            style A fill:#e1f5fe
            style B fill:#f3e5f5
            style C fill:#e8f5e8
            style D fill:#fff3e0
            style E fill:#fce4ec
        """
        
        return self.create_mermaid_chart(
            mermaid_code,
            f"{industry} Industry Value Chain",
            save_path
        )
    
    def create_ai_infrastructure_diagram(
        self,
        save_path: Optional[str] = None
    ) -> str:
        """
        Create AI infrastructure ecosystem diagram
        
        Args:
            save_path: Path to save chart
            
        Returns:
            Path to saved chart
        """
        mermaid_code = """
        graph TB
            subgraph "Hardware Layer"
                A[Computing Chips<br/>计算芯片]
                B[GPU/TPU<br/>图形处理器]
                C[Data Centers<br/>数据中心]
            end
            
            subgraph "Infrastructure Layer"
                D[Cloud Platforms<br/>云平台]
                E[Edge Computing<br/>边缘计算]
                F[Storage Systems<br/>存储系统]
            end
            
            subgraph "Platform Layer"
                G[AI Frameworks<br/>AI框架]
                H[ML Platforms<br/>机器学习平台]
                I[Data Processing<br/>数据处理]
            end
            
            subgraph "Application Layer"
                J[AI Models<br/>AI模型]
                K[Business Apps<br/>业务应用]
                L[Services<br/>服务]
            end
            
            A --> D
            B --> D
            C --> D
            D --> G
            E --> G
            F --> I
            G --> J
            H --> J
            I --> J
            J --> K
            K --> L
            
            style A fill:#ffcdd2
            style D fill:#c8e6c9
            style G fill:#bbdefb
            style J fill:#f8bbd9
        """
        
        return self.create_mermaid_chart(
            mermaid_code,
            "AI Infrastructure Ecosystem",
            save_path
        )

if __name__ == "__main__":
    # Test chart generator
    generator = ChartGenerator()
    
    # Create sample data
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    sample_df = pd.DataFrame({
        'trade_date': dates,
        'close': 100 + np.cumsum(np.random.randn(100) * 0.5),
        'vol': np.random.randint(1000000, 10000000, 100),
        'open': None,
        'high': None,
        'low': None
    })
    
    # Fill OHLC data
    sample_df['open'] = sample_df['close'].shift(1).fillna(sample_df['close'])
    sample_df['high'] = sample_df['close'] * 1.02
    sample_df['low'] = sample_df['close'] * 0.98
    
    print("Testing Chart Generator...")
    
    # Test stock price chart with English labels
    chart_path = generator.create_stock_price_chart(sample_df, "Stock Price Chart")
    print(f"Stock price chart: {chart_path}")
    
    # Test financial ratios chart with English labels
    ratios = {"ROE": 15.2, "ROA": 8.5, "Net Profit Margin": 12.3, "Gross Margin": 35.6}
    ratios_path = generator.create_financial_ratios_chart(ratios, "Financial Ratios")
    print(f"Financial ratios chart: {ratios_path}")
    
    # Test Mermaid charts
    industry_chain = generator.create_industry_chain_diagram()
    print(f"Industry chain diagram: {industry_chain}")
    
    ai_infrastructure = generator.create_ai_infrastructure_diagram()
    print(f"AI infrastructure diagram: {ai_infrastructure}")
    
    print("Chart Generator test completed.")