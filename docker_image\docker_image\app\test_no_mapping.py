#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试删除COMPANY_MAPPING后的功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_parse_company_input():
    """测试parse_company_input函数"""
    from run_company_research_report import parse_company_input
    
    print("🧪 测试 parse_company_input 函数")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        # 港股代码
        ("00020.HK", "港股代码 - 商汤科技"),
        ("06682.HK", "港股代码 - 4Paradigm"),
        ("00700.HK", "港股代码 - 腾讯控股"),
        
        # A股代码
        ("000001.SZ", "A股代码 - 平安银行"),
        ("600036.SH", "A股代码 - 招商银行"),
        ("600519.SH", "A股代码 - 贵州茅台"),
        
        # 美股代码
        ("AAPL", "美股代码 - 苹果"),
        ("MSFT", "美股代码 - 微软"),
        ("TSLA", "美股代码 - 特斯拉"),
        
        # 公司名称
        ("商汤科技", "公司名称"),
        ("腾讯控股", "公司名称"),
        ("平安银行", "公司名称"),
        ("苹果公司", "公司名称"),
    ]
    
    for input_value, description in test_cases:
        try:
            name, code = parse_company_input(input_value)
            print(f"✅ {description}")
            print(f"   输入: {input_value}")
            print(f"   输出: 名称={name}, 代码={code}")
            print()
        except Exception as e:
            print(f"❌ {description}")
            print(f"   输入: {input_value}")
            print(f"   错误: {e}")
            print()

def test_company_research_pipeline_init():
    """测试CompanyResearchReportPipeline初始化"""
    print("🧪 测试 CompanyResearchReportPipeline 初始化")
    print("=" * 50)
    
    try:
        from run_company_research_report import CompanyResearchReportPipeline
        
        # 测试不同的初始化方式
        test_cases = [
            ("商汤科技", None, "只提供公司名称"),
            ("商汤科技", "00020.HK", "提供公司名称和代码"),
            ("00020.HK", None, "只提供股票代码"),
        ]
        
        for company_name, company_code, description in test_cases:
            try:
                print(f"📋 测试: {description}")
                print(f"   公司名称: {company_name}")
                print(f"   股票代码: {company_code}")
                
                # 只测试初始化，不运行完整流程
                pipeline = CompanyResearchReportPipeline(
                    company_name=company_name,
                    company_code=company_code,
                    skip_steps=['1', '2', '3', '4', '5', '6']  # 跳过所有步骤
                )
                
                print(f"✅ 初始化成功")
                print(f"   最终公司名称: {pipeline.company_name}")
                print(f"   最终股票代码: {pipeline.company_code}")
                print()
                
            except Exception as e:
                print(f"❌ 初始化失败: {e}")
                print()
                
    except ImportError as e:
        print(f"❌ 导入失败: {e}")

def test_help_function():
    """测试帮助功能"""
    print("🧪 测试帮助功能")
    print("=" * 50)
    
    try:
        # 模拟命令行参数
        original_argv = sys.argv
        sys.argv = ['run_company_research_report.py', '--help']
        
        from run_company_research_report import main
        
        # 这会打印帮助信息并退出
        try:
            main()
        except SystemExit:
            print("✅ 帮助功能正常")
            
    except Exception as e:
        print(f"❌ 帮助功能测试失败: {e}")
    finally:
        sys.argv = original_argv

def main():
    """主测试函数"""
    print("🚀 COMPANY_MAPPING 删除后功能测试")
    print("=" * 60)
    print()
    
    # 测试解析函数
    test_parse_company_input()
    
    # 测试流水线初始化
    test_company_research_pipeline_init()
    
    # 测试帮助功能
    test_help_function()
    
    print("🎉 测试完成！")
    print()
    print("📋 使用说明:")
    print("现在系统不再依赖COMPANY_MAPPING映射表，而是:")
    print("1. 如果输入是股票代码格式，直接使用代码作为名称和代码")
    print("2. 如果输入是公司名称，直接使用名称作为名称和代码")
    print("3. 各个子模块会根据环境变量COMPANY_NAME和COMPANY_CODE进行处理")
    print()
    print("✅ 优势:")
    print("- 更简单的逻辑，减少维护成本")
    print("- 支持任意公司名称和股票代码")
    print("- 不需要预定义映射关系")
    print()
    print("⚠️  注意:")
    print("- 股票代码格式必须准确 (如: 00020.HK, 000001.SZ, AAPL)")
    print("- 公司名称会直接传递给各个模块处理")

if __name__ == "__main__":
    main()
