{"summary": {"total_checks": 36, "passed_checks": 36, "failed_checks": 0, "pass_rate": 100.0, "check_time": "2025-07-27T14:59:12.414680"}, "results": [{"category": "Python环境", "item": "Python版本", "status": true, "details": "当前版本: 3.12.1 (符合要求)", "timestamp": "2025-07-27T14:59:12.415680"}, {"category": "Python环境", "item": "UTF-8编码支持", "status": true, "details": "支持中文编码", "timestamp": "2025-07-27T14:59:12.415680"}, {"category": "Python模块", "item": "网络请求", "status": true, "details": "requests 已安装", "timestamp": "2025-07-27T14:59:12.915564"}, {"category": "Python模块", "item": "数据处理", "status": true, "details": "pandas 已安装", "timestamp": "2025-07-27T14:59:13.926987"}, {"category": "Python模块", "item": "数值计算", "status": true, "details": "numpy 已安装", "timestamp": "2025-07-27T14:59:13.926987"}, {"category": "Python模块", "item": "图表绘制", "status": true, "details": "matp<PERSON>lib 已安装", "timestamp": "2025-07-27T14:59:14.090407"}, {"category": "Python模块", "item": "网页解析", "status": true, "details": "beautifulsoup4 已安装", "timestamp": "2025-07-27T14:59:14.257437"}, {"category": "Python模块", "item": "OpenAI API", "status": true, "details": "openai 已安装", "timestamp": "2025-07-27T14:59:15.458377"}, {"category": "Python模块", "item": "JSON处理", "status": true, "details": "json 已安装", "timestamp": "2025-07-27T14:59:15.458377"}, {"category": "Python模块", "item": "CSV处理", "status": true, "details": "csv 已安装", "timestamp": "2025-07-27T14:59:15.458377"}, {"category": "Python模块", "item": "时间处理", "status": true, "details": "datetime 已安装", "timestamp": "2025-07-27T14:59:15.458377"}, {"category": "Python模块", "item": "日志记录", "status": true, "details": "logging 已安装", "timestamp": "2025-07-27T14:59:15.458377"}, {"category": "Python模块", "item": "子进程", "status": true, "details": "subprocess 已安装", "timestamp": "2025-07-27T14:59:15.458377"}, {"category": "Python模块", "item": "参数解析", "status": true, "details": "argparse 已安装", "timestamp": "2025-07-27T14:59:15.458377"}, {"category": "配置文件", "item": "config.py加载", "status": true, "details": "配置文件加载成功", "timestamp": "2025-07-27T14:59:15.464815"}, {"category": "配置文件", "item": "API密钥", "status": true, "details": "API密钥已配置", "timestamp": "2025-07-27T14:59:15.464815"}, {"category": "配置文件", "item": "LLM模型", "status": true, "details": "模型: deepseek/deepseek-chat-v3-0324:free", "timestamp": "2025-07-27T14:59:15.464815"}, {"category": "脚本文件", "item": "数据爬取脚本", "status": true, "details": "crawler.py 存在且可读", "timestamp": "2025-07-27T14:59:15.464815"}, {"category": "脚本文件", "item": "观点爬取脚本", "status": true, "details": "crawler_viewpoint.py 存在且可读", "timestamp": "2025-07-27T14:59:15.464815"}, {"category": "脚本文件", "item": "图表生成脚本", "status": true, "details": "visualize_report.py 存在且可读", "timestamp": "2025-07-27T14:59:15.465860"}, {"category": "脚本文件", "item": "财务分析脚本", "status": true, "details": "financial_agent.py 存在且可读", "timestamp": "2025-07-27T14:59:15.465860"}, {"category": "脚本文件", "item": "股吧观点分析脚本", "status": true, "details": "guba_viewpoint_agent.py 存在且可读", "timestamp": "2025-07-27T14:59:15.465860"}, {"category": "脚本文件", "item": "研报生成脚本", "status": true, "details": "research_report_generator.py 存在且可读", "timestamp": "2025-07-27T14:59:15.466858"}, {"category": "脚本文件", "item": "主程序脚本", "status": true, "details": "run_company_research_report.py 存在且可读", "timestamp": "2025-07-27T14:59:15.466858"}, {"category": "数据文件", "item": "主要财务指标数据", "status": true, "details": "主要指标.csv (7227字节)", "timestamp": "2025-07-27T14:59:15.466858"}, {"category": "数据文件", "item": "利润表数据", "status": true, "details": "利润表.csv (40231字节)", "timestamp": "2025-07-27T14:59:15.466858"}, {"category": "数据文件", "item": "资产负债表数据", "status": true, "details": "资产负债表.csv (57336字节)", "timestamp": "2025-07-27T14:59:15.466858"}, {"category": "数据文件", "item": "现金流量表数据", "status": true, "details": "现金流量表.csv (51877字节)", "timestamp": "2025-07-27T14:59:15.466858"}, {"category": "输出目录", "item": "分析报告目录", "status": true, "details": "analysis_reports 存在且可写", "timestamp": "2025-07-27T14:59:15.468329"}, {"category": "输出目录", "item": "图表图片目录", "status": true, "details": "report_images 存在且可写", "timestamp": "2025-07-27T14:59:15.468329"}, {"category": "输出目录", "item": "通用输出目录", "status": true, "details": "outputs 已创建", "timestamp": "2025-07-27T14:59:15.469403"}, {"category": "网络连接", "item": "OpenRouter API", "status": true, "details": "连接正常", "timestamp": "2025-07-27T14:59:17.227896"}, {"category": "命令解析", "item": "公司名称解析", "status": true, "details": "4Paradigm -> 4Paradigm, 4Paradigm", "timestamp": "2025-07-27T14:59:17.241560"}, {"category": "命令解析", "item": "港股代码解析", "status": true, "details": "06682.HK -> 4Paradigm, 06682.HK", "timestamp": "2025-07-27T14:59:17.241560"}, {"category": "命令解析", "item": "A股代码解析", "status": true, "details": "000001.SZ -> 平安银行, 000001.SZ", "timestamp": "2025-07-27T14:59:17.242108"}, {"category": "命令解析", "item": "美股代码解析", "status": true, "details": "AAPL -> 苹果公司, AAPL", "timestamp": "2025-07-27T14:59:17.242108"}]}