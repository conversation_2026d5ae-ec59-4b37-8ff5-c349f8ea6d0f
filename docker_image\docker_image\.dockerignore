# Git相关
.git
.gitignore
.gitattributes

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
.cache/

# 测试相关
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# 文档
docs/
*.md
README*

# 配置文件（可能包含敏感信息）
.env
.env.local
.env.*.local

# 数据文件（根据需要调整）
*.csv
*.xlsx
*.xls
data/

# 输出文件
outputs/
reports/
analysis_reports/
report_images/

# Docker相关
Dockerfile*
docker-compose*
.dockerignore
