#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股吧观点数据清洗与总结Agent
处理guba_viewpoint.csv数据，进行清洗、总结并注明来源
使用deepseek/deepseek-chat-v3-0324:free模型
"""

import pandas as pd
import json
import re
import os
import time
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import requests
from config import config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('guba_viewpoint_agent.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ViewpointConfig:
    """观点分析配置"""
    # 模型配置 - 使用指定的deepseek模型
    model: str = "deepseek/deepseek-chat-v3-0324:free"
    api_key: str = config.llm.api_key
    base_url: str = config.llm.base_url
    max_tokens: int = 2000
    temperature: float = 0.3
    
    # 数据处理配置
    input_file: str = "guba_viewpoint.csv"
    output_file: str = "guba_viewpoint_analysis.json"
    min_content_length: int = 20  # 最小内容长度
    max_content_length: int = 3000  # 最大内容长度
    batch_size: int = 5  # 批量处理大小

class GubaViewpointAgent:
    """股吧观点分析Agent"""
    
    def __init__(self, config: ViewpointConfig = None):
        self.config = config or ViewpointConfig()
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {self.config.api_key}',
            'Content-Type': 'application/json'
        })
        
    def load_data(self) -> pd.DataFrame:
        """加载CSV数据"""
        try:
            df = pd.read_csv(self.config.input_file, encoding='utf-8-sig')
            logger.info(f"成功加载数据，共{len(df)}条记录")
            return df
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            raise
    
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗数据"""
        logger.info("开始数据清洗...")
        
        # 删除重复记录
        original_count = len(df)
        df = df.drop_duplicates(subset=['title', 'content'])
        logger.info(f"删除重复记录: {original_count - len(df)}条")
        
        # 清理内容字段
        df['content'] = df['content'].fillna('')
        df['content'] = df['content'].astype(str)
        
        # 过滤内容长度
        df = df[
            (df['content'].str.len() >= self.config.min_content_length) &
            (df['content'].str.len() <= self.config.max_content_length)
        ]
        logger.info(f"过滤后剩余记录: {len(df)}条")
        
        # 清理HTML标签和特殊字符
        df['content'] = df['content'].apply(self._clean_text)
        
        # 处理时间格式 - 修复缺少年份的问题
        df['publish_time'] = df['publish_time'].apply(self._parse_time)
        
        # 填充缺失值
        df['author'] = df['author'].fillna('未知作者')
        df['read_count'] = pd.to_numeric(df['read_count'], errors='coerce').fillna(0)
        df['reply_count'] = pd.to_numeric(df['reply_count'], errors='coerce').fillna(0)
        
        logger.info("数据清洗完成")
        return df
    
    def _clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not isinstance(text, str):
            return ""

        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)

        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)

        # 移除特殊字符
        text = re.sub(r'[^\u4e00-\u9fff\w\s.,!?;:()（）。，！？；：]', '', text)

        return text.strip()

    def _parse_time(self, time_str: str) -> str:
        """解析时间字符串，处理缺少年份的情况"""
        if pd.isna(time_str) or not isinstance(time_str, str):
            return ""

        try:
            # 处理 "06-26 10:28" 格式，添加当前年份
            if re.match(r'^\d{2}-\d{2}\s+\d{2}:\d{2}$', time_str.strip()):
                current_year = datetime.now().year
                time_str = f"{current_year}-{time_str.strip()}"
                parsed_time = datetime.strptime(time_str, "%Y-%m-%d %H:%M")
                return parsed_time.strftime("%Y-%m-%d %H:%M:%S")

            # 尝试其他常见格式
            for fmt in ["%Y-%m-%d %H:%M:%S", "%Y-%m-%d %H:%M", "%Y/%m/%d %H:%M"]:
                try:
                    parsed_time = datetime.strptime(time_str, fmt)
                    return parsed_time.strftime("%Y-%m-%d %H:%M:%S")
                except ValueError:
                    continue

            # 如果都无法解析，返回原始字符串
            return time_str

        except Exception as e:
            logger.warning(f"时间解析失败: {time_str}, 错误: {e}")
            return time_str
    
    def call_llm(self, prompt: str, max_retries: int = 3) -> str:
        """调用LLM API，包含重试机制和速率限制"""
        for attempt in range(max_retries):
            try:
                # 添加请求间隔以避免速率限制
                if hasattr(self, '_last_request_time'):
                    time_since_last = time.time() - self._last_request_time
                    min_interval = 2.0  # 最小间隔2秒
                    if time_since_last < min_interval:
                        wait_time = min_interval - time_since_last
                        logger.info(f"等待 {wait_time:.1f} 秒以避免速率限制...")
                        time.sleep(wait_time)

                self._last_request_time = time.time()

                payload = {
                    "model": self.config.model,
                    "messages": [
                        {
                            "role": "system",
                            "content": "你是一个专业的金融分析师，擅长分析股市观点和投资建议。请用中文回答。"
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "max_tokens": self.config.max_tokens,
                    "temperature": self.config.temperature
                }

                logger.info(f"正在调用LLM API... (尝试 {attempt + 1}/{max_retries})")

                response = self.session.post(
                    f"{self.config.base_url}/chat/completions",
                    json=payload,
                    timeout=90  # 增加超时时间
                )

                # 特别处理429错误
                if response.status_code == 429:
                    logger.warning(f"遇到429速率限制错误，等待后重试...")
                    retry_delay = (attempt + 1) * 15  # 递增延迟：15s, 30s, 45s
                    time.sleep(retry_delay)
                    continue

                response.raise_for_status()

                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    logger.info("LLM调用成功")
                    return content
                else:
                    logger.warning(f"LLM返回格式异常: {result}")
                    if attempt < max_retries - 1:
                        time.sleep(5)
                        continue
                    return f"分析失败: 返回格式异常"

            except requests.exceptions.Timeout:
                logger.warning(f"LLM调用超时 (尝试 {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    time.sleep(10)
                    continue
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 429:
                    logger.warning(f"速率限制错误 (尝试 {attempt + 1}/{max_retries})")
                    retry_delay = (attempt + 1) * 20  # 更长的延迟
                    time.sleep(retry_delay)
                    continue
                else:
                    logger.error(f"HTTP错误: {e}")
                    if attempt < max_retries - 1:
                        time.sleep(5)
                        continue
            except Exception as e:
                logger.error(f"LLM调用异常 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(5)
                    continue

        logger.error(f"LLM调用最终失败，已重试{max_retries}次")
        return f"分析失败: 多次重试后仍然失败"
    
    def analyze_viewpoint(self, row: pd.Series) -> Dict[str, Any]:
        """分析单个观点"""
        prompt = f"""
请分析以下股吧观点内容，提供结构化的分析结果：

标题: {row['title']}
作者: {row['author']}
发布时间: {row['publish_time']}
阅读数: {row['read_count']}
回复数: {row['reply_count']}
内容: {row['content'][:500]}...

请从以下几个维度进行分析：
1. 观点类型（看涨/看跌/中性）
2. 主要论点（3-5个要点）
3. 情感倾向（积极/消极/中性）
4. 可信度评估（1-5分）
5. 关键词提取（5-8个）
6. 一句话总结

请以JSON格式返回分析结果。
"""
        
        analysis_text = self.call_llm(prompt)
        
        # 尝试解析JSON，如果失败则返回原始文本
        try:
            # 提取JSON部分
            json_match = re.search(r'\{.*\}', analysis_text, re.DOTALL)
            if json_match:
                analysis = json.loads(json_match.group())
            else:
                analysis = {"raw_analysis": analysis_text}
        except:
            analysis = {"raw_analysis": analysis_text}
        
        # 添加来源信息
        analysis.update({
            "source": {
                "title": row['title'],
                "url": row['url'],
                "author": row['author'],
                "author_url": row['author_url'],
                "publish_time": row['publish_time'] if row['publish_time'] else "未知时间",
                "read_count": int(row['read_count']) if pd.notna(row['read_count']) else 0,
                "reply_count": int(row['reply_count']) if pd.notna(row['reply_count']) else 0
            }
        })
        
        return analysis
    
    def process_batch(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """批量处理观点数据"""
        results = []
        total_batches = (len(df) + self.config.batch_size - 1) // self.config.batch_size
        
        for i in range(0, len(df), self.config.batch_size):
            batch_num = i // self.config.batch_size + 1
            batch_df = df.iloc[i:i + self.config.batch_size]
            
            logger.info(f"处理批次 {batch_num}/{total_batches}，共{len(batch_df)}条记录")
            
            for idx, row in batch_df.iterrows():
                try:
                    analysis = self.analyze_viewpoint(row)
                    results.append(analysis)
                    logger.info(f"完成分析: {row['title'][:30]}...")
                except Exception as e:
                    logger.error(f"分析失败 {row['title']}: {e}")
                    continue
        
        return results
    
    def generate_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成总体分析摘要"""
        if not results:
            return {"error": "没有有效的分析结果"}
        
        # 统计信息
        total_count = len(results)
        
        # 提取关键信息用于总结
        titles = [r.get('source', {}).get('title', '') for r in results[:10]]
        summary_prompt = f"""
基于以下{total_count}条股吧观点数据，请生成一份综合分析报告：

样本标题（前10条）:
{chr(10).join([f"- {title}" for title in titles])}

请提供：
1. 整体市场情绪分析
2. 主要观点趋势
3. 热点话题总结
4. 投资者关注焦点
5. 风险提示

请用中文回答，格式清晰。
"""
        
        summary_text = self.call_llm(summary_prompt)
        
        return {
            "total_analyzed": total_count,
            "analysis_time": datetime.now().isoformat(),
            "model_used": self.config.model,
            "summary": summary_text,
            "data_source": "东方财富股吧观点数据"
        }
    
    def save_results(self, results: List[Dict[str, Any]], summary: Dict[str, Any]):
        """保存分析结果"""
        output_data = {
            "metadata": summary,
            "analyses": results
        }
        
        with open(self.config.output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"分析结果已保存到: {self.config.output_file}")
    
    def run(self):
        """运行完整的分析流程"""
        try:
            logger.info("开始股吧观点分析...")
            
            # 1. 加载数据
            df = self.load_data()
            
            # 2. 清洗数据
            df_clean = self.clean_data(df)
            
            # 3. 批量分析
            results = self.process_batch(df_clean)
            
            # 4. 生成摘要
            summary = self.generate_summary(results)
            
            # 5. 保存结果
            self.save_results(results, summary)
            
            logger.info(f"分析完成！共处理{len(results)}条观点")
            return True
            
        except Exception as e:
            logger.error(f"分析过程出错: {e}")
            return False

def main():
    """主函数"""
    agent = GubaViewpointAgent()
    success = agent.run()
    
    if success:
        print("[成功] 股吧观点分析完成")
        print(f"[图表] 结果文件: {agent.config.output_file}")
    else:
        print("[失败] 分析失败，请查看日志")

if __name__ == "__main__":
    main()
