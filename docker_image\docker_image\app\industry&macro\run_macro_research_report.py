#!/usr/bin/env python3
"""
独立的宏观研究报告生成器
支持通过命令行参数指定宏观主题和时间范围生成专业的宏观研究报告

使用方法:
python run_macro_research_report.py --macro_name "生成式AI基建与算力投资趋势" --time "2023-2026"
python run_macro_research_report.py --macro_name "全球通胀与货币政策" --time "2024-2025"
"""

import argparse
import asyncio
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH

# 导入现有模块
from data_collector import DataCollector
from llm_client import FinancialAnalysisAgent
from chart_generator import ChartGenerator
from config import get_config, REPORT_TYPES

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MacroResearchGenerator:
    """独立的宏观研究报告生成器"""
    
    def __init__(self):
        """初始化宏观研究生成器"""
        self.data_collector = DataCollector()
        self.analysis_agent = FinancialAnalysisAgent()
        self.chart_generator = ChartGenerator()
        self.config = get_config()
        
        # 宏观主题映射字典
        self.macro_mapping = {
            "生成式AI基建与算力投资趋势": "AI基建",
            "全球通胀与货币政策": "通胀政策",
            "数字经济发展趋势": "数字经济",
            "绿色金融与ESG投资": "绿色金融",
            "全球供应链重构": "供应链",
            "新兴市场投资机会": "新兴市场",
            "科技创新与产业升级": "科技创新",
            "房地产市场调控": "房地产",
            "能源转型与投资": "能源转型",
            "国际贸易格局": "国际贸易",
            "金融科技监管": "金融监管",
            "人口老龄化影响": "人口结构"
        }
        
        logger.info("宏观研究生成器初始化完成")
    
    async def collect_macro_data(self, macro_name: str, time_period: str) -> Dict[str, Any]:
        """
        收集宏观相关数据
        
        Args:
            macro_name: 宏观主题名称
            time_period: 时间范围
            
        Returns:
            包含宏观数据的字典
        """
        logger.info(f"开始收集宏观数据: {macro_name} ({time_period})")
        
        try:
            # 解析时间范围
            start_year, end_year = self._parse_time_period(time_period)
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = datetime(start_year, 1, 1).strftime('%Y%m%d')
            
            # 获取核心宏观指标
            macro_indicators = REPORT_TYPES["macro"]["required_indicators"]
            macro_data = {}
            
            for indicator in macro_indicators:
                try:
                    data = self.data_collector.get_macro_data(indicator, start_date, end_date)
                    if not data.empty:
                        macro_data[indicator] = data
                except Exception as e:
                    logger.warning(f"获取{indicator}数据失败: {e}")
            
            # 获取主要股指数据作为市场背景
            major_indices = ['000001.SH', '399001.SZ', '000300.SH']  # 上证、深证、沪深300
            index_data = {}
            for index_code in major_indices:
                try:
                    data = self.data_collector.get_index_data(index_code, start_date, end_date)
                    if not data.empty:
                        index_data[index_code] = data
                except Exception as e:
                    logger.warning(f"获取指数{index_code}数据失败: {e}")
            
            # 获取市场情绪数据
            market_sentiment = self.data_collector.get_market_sentiment()
            
            data_package = {
                "macro_name": macro_name,
                "time_period": time_period,
                "start_year": start_year,
                "end_year": end_year,
                "macro_data": macro_data,
                "index_data": index_data,
                "market_sentiment": market_sentiment,
                "collection_timestamp": datetime.now().isoformat(),
                "data_sources": ["Tushare", "公开市场数据", "宏观统计数据"]
            }
            
            logger.info(f"宏观数据收集完成: {macro_name}")
            return data_package
            
        except Exception as e:
            logger.error(f"收集宏观数据时出错: {e}")
            return {"error": str(e), "macro_name": macro_name}
    
    def _parse_time_period(self, time_period: str) -> tuple:
        """解析时间范围字符串"""
        try:
            if '-' in time_period:
                start_str, end_str = time_period.split('-')
                start_year = int(start_str.strip())
                end_year = int(end_str.strip())
                return start_year, end_year
            else:
                # 单年份，默认为当前年份到指定年份
                target_year = int(time_period.strip())
                current_year = datetime.now().year
                return current_year, target_year
        except:
            # 默认时间范围
            current_year = datetime.now().year
            return current_year, current_year + 3
    
    async def analyze_macro_theme(self, macro_name: str, time_period: str, data_package: Dict[str, Any]) -> Dict[str, str]:
        """
        进行宏观主题分析
        
        Args:
            macro_name: 宏观主题名称
            time_period: 时间范围
            data_package: 宏观数据包
            
        Returns:
            包含各个分析章节的字典
        """
        logger.info(f"开始宏观分析: {macro_name} ({time_period})")
        
        analysis_sections = {}
        
        try:
            # 准备数据上下文
            data_context = self._prepare_macro_context(macro_name, time_period, data_package)
            
            # 生成各个分析章节
            sections = [
                ("executive_summary", "执行摘要"),
                ("global_overview", "全球概况"),
                ("trend_analysis", "趋势分析"),
                ("policy_environment", "政策环境"),
                ("market_forecast", "市场预测"),
                ("investment_opportunities", "投资机会"),
                ("risk_assessment", "风险评估"),
                ("conclusion", "结论与建议")
            ]
            
            for section_key, section_name in sections:
                logger.info(f"生成章节: {section_name}")
                
                content = await self._generate_macro_section_content(
                    section_key, macro_name, time_period, data_context
                )
                analysis_sections[section_key] = content
                
                # 避免API限流
                await asyncio.sleep(1)
            
            logger.info(f"宏观分析完成: {macro_name}")
            return analysis_sections
            
        except Exception as e:
            logger.error(f"宏观分析时出错: {e}")
            return {"error": str(e)}
    
    def _prepare_macro_context(self, macro_name: str, time_period: str, data_package: Dict[str, Any]) -> str:
        """准备宏观分析的数据上下文"""
        context_parts = [
            f"宏观主题: {macro_name}",
            f"分析时间范围: {time_period}",
            f"数据收集时间: {data_package.get('collection_timestamp', '未知')}",
        ]
        
        # 添加宏观数据摘要
        macro_data = data_package.get('macro_data', {})
        if macro_data:
            context_parts.append("核心宏观指标:")
            for indicator, data in macro_data.items():
                if not data.empty:
                    try:
                        latest_value = data.iloc[-1]
                        context_parts.append(f"  {indicator}: {latest_value}")
                    except:
                        context_parts.append(f"  {indicator}: 数据可用")
        
        # 添加市场指数数据
        index_data = data_package.get('index_data', {})
        if index_data:
            context_parts.append("主要市场指数:")
            for index_code, data in index_data.items():
                if not data.empty:
                    try:
                        latest_close = data['close'].iloc[-1]
                        context_parts.append(f"  {index_code}: {latest_close:.2f}")
                    except:
                        context_parts.append(f"  {index_code}: 数据可用")
        
        # 添加市场情绪数据
        sentiment = data_package.get('market_sentiment', {})
        if sentiment:
            context_parts.append("市场情绪指标:")
            for key, value in sentiment.items():
                context_parts.append(f"  {key}: {value}")
        
        return "\n".join(context_parts)
    
    async def _generate_macro_section_content(self, section_type: str, macro_name: str, time_period: str, data_context: str) -> str:
        """生成特定章节的内容"""
        
        section_prompts = {
            "executive_summary": f"""
            请为{macro_name}（{time_period}）撰写执行摘要，包括：
            1. 宏观主题发展现状和趋势
            2. 主要驱动因素分析
            3. 政策环境和影响
            4. 投资机会和风险概述
            5. 核心结论和建议
            要求宏观视角，约300-400字。
            """,
            
            "global_overview": f"""
            请分析{macro_name}的全球概况，包括：
            1. 全球发展现状和格局
            2. 主要国家和地区比较
            3. 国际合作与竞争态势
            4. 全球化影响和趋势
            5. 跨境投资和贸易影响
            要求全球视野，约500-600字。
            """,
            
            "trend_analysis": f"""
            请分析{macro_name}在{time_period}期间的趋势，包括：
            1. 历史发展轨迹
            2. 当前发展阶段特征
            3. 未来发展趋势预测
            4. 关键转折点和里程碑
            5. 技术和模式创新
            要求趋势明确，约500-600字。
            """,
            
            "policy_environment": f"""
            请分析{macro_name}的政策环境，包括：
            1. 相关政策法规框架
            2. 政府支持和监管措施
            3. 国际政策协调
            4. 政策变化趋势
            5. 政策风险和机遇
            要求政策深度，约400-500字。
            """,
            
            "market_forecast": f"""
            请预测{macro_name}在{time_period}的市场前景，包括：
            1. 市场规模和增长预测
            2. 关键发展里程碑
            3. 技术突破和应用
            4. 竞争格局演变
            5. 不确定性因素
            要求预测合理，约500-600字。
            """,
            
            "investment_opportunities": f"""
            请分析{macro_name}的投资机会，包括：
            1. 主要投资领域和方向
            2. 投资价值和回报预期
            3. 投资时机和策略
            4. 区域和行业机会
            5. 投资建议和配置
            要求实用性强，约400-500字。
            """,
            
            "risk_assessment": f"""
            请评估{macro_name}的相关风险，包括：
            1. 宏观经济风险
            2. 政策和监管风险
            3. 技术和市场风险
            4. 地缘政治风险
            5. 风险防控建议
            要求全面客观，约400-500字。
            """,
            
            "conclusion": f"""
            请为{macro_name}（{time_period}）研究报告撰写结论与建议，包括：
            1. 核心观点总结
            2. 战略建议和方向
            3. 关键风险提示
            4. 后续关注要点
            5. 政策建议
            要求简洁有力，约300-400字。
            """
        }
        
        prompt = section_prompts.get(section_type, f"请分析{macro_name}的{section_type}相关内容。")
        
        messages = [
            {"role": "system", "content": f"""你是一位专业的宏观经济分析师，擅长撰写深度的宏观研究报告。
            请基于以下数据背景进行分析：
            {data_context}
            
            要求：
            1. 分析专业深入，具有宏观视野
            2. 语言规范，符合研报标准
            3. 结合实际数据和市场情况
            4. 保持客观中性的分析态度
            5. 注重前瞻性和战略性
            """},
            {"role": "user", "content": prompt}
        ]
        
        return self.analysis_agent.llm_client.create_completion(
            messages, max_tokens=1200, temperature=0.3
        )
    
    async def generate_macro_charts(self, macro_name: str, time_period: str, data_package: Dict[str, Any]) -> List[str]:
        """
        生成宏观相关图表
        
        Args:
            macro_name: 宏观主题名称
            time_period: 时间范围
            data_package: 宏观数据包
            
        Returns:
            生成的图表文件路径列表
        """
        logger.info(f"开始生成宏观图表: {macro_name}")
        
        chart_paths = []
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        try:
            # 创建图表输出目录
            chart_dir = "./macro_reports/charts"
            os.makedirs(chart_dir, exist_ok=True)
            
            # 示例：宏观指标趋势图
            macro_data = data_package.get('macro_data', {})
            if 'GDP' in macro_data and not macro_data['GDP'].empty:
                gdp_data = macro_data['GDP']
                # 这里可以生成GDP趋势图
                logger.info("GDP数据可用，可生成趋势图")
            
            # 示例：投资结构饼图
            investment_structure = {
                "基础设施": 40.0,
                "技术研发": 30.0,
                "人才培养": 20.0,
                "其他": 10.0
            }
            
            pie_chart_path = os.path.join(chart_dir, f"investment_structure_{timestamp}.png")
            self.chart_generator.create_pie_chart(
                investment_structure,
                title=f"{macro_name}投资结构分析",
                save_path=pie_chart_path
            )
            chart_paths.append(pie_chart_path)
            
            logger.info(f"图表生成完成，共生成{len(chart_paths)}个图表")
            return chart_paths
            
        except Exception as e:
            logger.error(f"生成图表时出错: {e}")
            return []
    
    async def generate_report(self, macro_name: str, time_period: str) -> str:
        """
        生成完整的宏观研究报告
        
        Args:
            macro_name: 宏观主题名称
            time_period: 时间范围
            
        Returns:
            生成的报告文件路径
        """
        logger.info(f"开始生成{macro_name}宏观研究报告 ({time_period})")
        
        try:
            # 1. 收集数据
            print(f"📊 正在收集{macro_name}宏观数据...")
            data_package = await self.collect_macro_data(macro_name, time_period)
            
            if "error" in data_package:
                raise Exception(f"数据收集失败: {data_package['error']}")
            
            # 2. 进行分析
            print(f"🔍 正在分析{macro_name}宏观趋势...")
            analysis_results = await self.analyze_macro_theme(macro_name, time_period, data_package)
            
            if "error" in analysis_results:
                raise Exception(f"宏观分析失败: {analysis_results['error']}")
            
            # 3. 生成图表
            print(f"📈 正在生成{macro_name}宏观图表...")
            chart_paths = await self.generate_macro_charts(macro_name, time_period, data_package)
            
            # 4. 生成报告文件
            print(f"📝 正在生成{macro_name}宏观报告...")
            report_path = self._generate_docx_report(macro_name, time_period, analysis_results, chart_paths)
            
            logger.info(f"宏观研究报告生成完成: {report_path}")
            return report_path

        except Exception as e:
            logger.error(f"生成报告时出错: {e}")
            raise

    def _generate_docx_report(self, macro_name: str, time_period: str, analysis_results: Dict[str, str], chart_paths: List[str]) -> str:
        """生成DOCX格式报告"""
        output_dir = "./macro_reports"
        os.makedirs(output_dir, exist_ok=True)

        doc = Document()

        # 标题
        title = doc.add_heading(f'{macro_name}宏观研究报告', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 副标题
        subtitle = doc.add_heading(f'分析期间：{time_period}', level=2)
        subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 元数据
        doc.add_paragraph(f"生成时间: {datetime.now().strftime('%Y年%m月%d日')}")
        doc.add_paragraph(f"分析师: AI智能分析系统")
        doc.add_paragraph(f"报告类型: 宏观经济研究报告")
        doc.add_paragraph("")

        # 添加各个章节
        section_titles = {
            "executive_summary": "执行摘要",
            "global_overview": "全球概况",
            "trend_analysis": "趋势分析",
            "policy_environment": "政策环境",
            "market_forecast": "市场预测",
            "investment_opportunities": "投资机会",
            "risk_assessment": "风险评估",
            "conclusion": "结论与建议"
        }

        for section_key, section_title in section_titles.items():
            if section_key in analysis_results:
                doc.add_heading(section_title, level=1)
                doc.add_paragraph(analysis_results[section_key])
                doc.add_paragraph("")

        # 添加图表分析章节
        if chart_paths:
            doc.add_heading('图表分析', level=1)
            doc.add_paragraph("以下图表展示了相关的宏观经济数据和趋势分析：")
            for chart_path in chart_paths:
                if os.path.exists(chart_path):
                    doc.add_paragraph(f"图表: {os.path.basename(chart_path)}")

        # 添加免责声明
        doc.add_heading('免责声明', level=1)
        disclaimer_text = """
        本报告由AI系统自动生成，基于公开数据和模型分析。报告内容仅供参考，不构成投资建议。
        宏观经济分析涉及众多不确定因素，实际情况可能与预测存在差异。
        投资者应结合自身情况和专业建议做出决策。投资有风险，决策需谨慎。
        """
        doc.add_paragraph(disclaimer_text.strip())

        # 保存文件
        filename = "Macro_Research_Report.docx"
        report_path = os.path.join(output_dir, filename)
        doc.save(report_path)

        return report_path

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='宏观研究报告生成器')
    parser.add_argument('--macro_name', '-m', type=str, required=True,
                       help='宏观主题名称 (例如: 生成式AI基建与算力投资趋势)')
    parser.add_argument('--time', '-t', type=str, required=True,
                       help='时间范围 (例如: 2023-2026, 2024-2025)')

    args = parser.parse_args()

    print("🌍 宏观研究报告生成器")
    print("=" * 50)
    print(f"📋 宏观主题: {args.macro_name}")
    print(f"⏰ 时间范围: {args.time}")
    print()

    try:
        generator = MacroResearchGenerator()

        # 验证宏观主题名称
        if args.macro_name not in generator.macro_mapping:
            print(f"⚠️  宏观主题 '{args.macro_name}' 可能不在预设列表中")
            print("支持的宏观主题包括:", ", ".join(generator.macro_mapping.keys()))
            print("将尝试继续生成...")
            print()

        # 生成报告
        report_path = await generator.generate_report(args.macro_name, args.time)

        print("✅ 报告生成完成!")
        print(f"📁 报告路径: {report_path}")
        print(f"📊 报告格式: DOCX")

        if os.path.exists(report_path):
            file_size = os.path.getsize(report_path) / 1024  # KB
            print(f"📏 文件大小: {file_size:.1f} KB")

        print("\n🎯 报告已保存到 macro_reports 目录")

    except KeyboardInterrupt:
        print("\n⏹️  操作被用户取消")
    except Exception as e:
        print(f"❌ 生成报告时出错: {e}")
        logger.error(f"报告生成失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())
