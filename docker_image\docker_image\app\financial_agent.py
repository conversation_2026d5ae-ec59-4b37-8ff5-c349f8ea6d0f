#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财务分析Agent主模块
基于LLM的智能财务分析系统
"""

import os

class FinancialAgent:
    """财务分析Agent（已合并所有功能，直接分析全部CSV数据，无日志）"""
    def __init__(self):
        from config import config, validate_config
        self.file_path = config.analysis.main_indicators_file
        self.api_key = config.llm.api_key
        self.base_url = config.llm.base_url
        self.model = config.llm.model
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/FinAgentX",
            "X-Title": "FinAgentX Financial Analysis"
        }

    def initialize(self) -> bool:
        """初始化Agent"""
        try:
            from config import validate_config
            validate_config()
            if not os.path.exists(self.file_path):
                raise Exception("主要指标.csv文件不存在")
            return True
        except Exception as e:
            print(f"❌ Agent初始化失败: {e}")
            return False

    def generate_report(self) -> str:
        """直接将主要指标.csv的全部数据交给LLM分析，生成报告"""
        try:
            import pandas as pd
            df = pd.read_csv(self.file_path, encoding='utf-8-sig')
            csv_text = df.to_csv(index=False)
            prompt = f"""
请对以下财务数据进行全面分析，生成专业的Markdown格式财务分析报告（包含财务表现、盈利能力、成长性、财务健康度以及你认为需要的其他内容），以文字和表格为主，直接输出完整报告：

{csv_text}
"""
            payload = {
                "model": self.model,
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "max_tokens": 4096,
                "temperature": 0.7,
                "top_p": 1.0,
                "stream": False
            }
            import requests
            from config import config
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=config.llm.timeout
            )
            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                return content
            else:
                return f"# 报告生成失败\n\nAPI错误: {response.status_code} {response.text}"
        except Exception as e:
            return f"# 报告生成失败\n\n错误信息: {e}"

    def save_report(self, report: str, filename: str = None) -> str:
        """保存报告"""
        if not filename:
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"财务分析报告_{timestamp}.md"
        from config import config
        filepath = os.path.join(config.analysis.output_dir, filename)
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(report)
            return filepath
        except Exception as e:
            return ""

# 创建全局Agent实例
financial_agent = FinancialAgent()

if __name__ == "__main__":
    print("[启动] 启动财务分析Agent...")
    if financial_agent.initialize():
        print("[生成] 生成财务分析报告...")
        report = financial_agent.generate_report()
        if report:
            filepath = financial_agent.save_report(report)
            if filepath:
                print(f"[完成] 分析完成！报告已保存至: {filepath}")
            else:
                print("[失败] 报告保存失败")
        else:
            print("[失败] 报告生成失败")
    else:
        print("[失败] Agent初始化失败")
